<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDF合同对比系统 - TypeScript版本</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #4083f7;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .button {
            background: #4083f7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .button:hover {
            background: #3066d6;
        }
        
        .pdf-container {
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
            background: #fafafa;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PDF合同对比系统</h1>
            <p>TypeScript重构版本演示</p>
        </div>
        
        <div class="content">
            <div class="demo-section">
                <h3>1. 基本使用方法</h3>
                <p>以下是如何使用重构后的TypeScript版本：</p>
                <div class="code-block">
import { initPDFCompare } from './src/main.js';
import type { CompareItem } from './src/types/index.js';

// 准备对比数据
const item: CompareItem = {
    srcName: "技术开发（委托）合同版.docx",
    targetName: "技术开发（委托）合同版（条款差异）.docx",
    srcPdf: "path/to/source.pdf",
    targetPdf: "path/to/target.pdf",
    srcPages: 8,
    targetPages: 8,
    objectId: "unique-object-id",
    revisions: [
        {
            type: 2, // 1=新增，2=删除，3=修改
            id: 1,
            srcRev: {
                startPos: 2347,
                endPos: 2348,
                content: "整",
                page: 4
            },
            targetRev: {
                startPos: 2347,
                endPos: 2347,
                content: "",
                page: 4
            }
        }
        // ... 更多差异项
    ]
};

// 初始化PDF对比
const pdfCompared = initPDFCompare(item);
                </div>
            </div>
            
            <div class="demo-section">
                <h3>2. 主要特性</h3>
                <ul>
                    <li><strong>类型安全</strong>：完整的TypeScript类型定义</li>
                    <li><strong>模块化设计</strong>：清晰的类和接口分离</li>
                    <li><strong>事件驱动</strong>：基于事件的交互机制</li>
                    <li><strong>可扩展性</strong>：易于扩展和维护的架构</li>
                    <li><strong>兼容性</strong>：保持与原有功能的兼容</li>
                </ul>
            </div>
            
            <div class="demo-section">
                <h3>3. 核心类结构</h3>
                <div class="code-block">
// 主要类
- PDFCompared: 主控制器类，管理整个对比流程
- PDFH5: PDF渲染引擎，处理单个PDF的显示
- EventManager: 事件管理器，处理各种交互事件
- ColorHelper: 颜色处理工具类
- JerryRect: 矩形绘制类
- SvgLine: SVG线条绘制类

// 主要接口
- CompareItem: 对比数据接口
- Revision: 差异项接口
- PDFOptions: PDF选项接口
- RectOptions: 矩形选项接口
                </div>
            </div>
            
            <div class="demo-section">
                <h3>4. 开发和构建</h3>
                <div class="code-block">
# 安装依赖
npm install

# 开发模式
npm run dev

# 类型检查
npm run type-check

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
                </div>
            </div>
            
            <div class="demo-section">
                <h3>5. 演示区域</h3>
                <p>这里可以放置实际的PDF对比演示：</p>
                <div class="pdf-container" id="pdfContainer">
                    <div class="loading">
                        PDF对比组件将在这里加载<br>
                        <small>需要提供实际的PDF文件和差异数据</small>
                    </div>
                </div>
                <div style="margin-top: 10px;">
                    <button class="button" onclick="loadDemo()">加载演示数据</button>
                    <button class="button" onclick="clearDemo()">清除演示</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function loadDemo() {
            alert('演示功能需要实际的PDF文件和服务器支持');
        }
        
        function clearDemo() {
            const container = document.getElementById('pdfContainer');
            container.innerHTML = '<div class="loading">PDF对比组件将在这里加载<br><small>需要提供实际的PDF文件和差异数据</small></div>';
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PDF合同对比系统演示页面已加载');
        });
    </script>
</body>
</html>
