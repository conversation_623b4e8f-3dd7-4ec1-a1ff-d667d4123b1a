# PDF合同对比系统 - TypeScript重构版

这是一个将原有JavaScript PDF合同对比系统重构为TypeScript的项目，提供了类型安全、模块化和可维护的代码结构。

## 🚀 主要特性

- **类型安全**: 完整的TypeScript类型定义
- **模块化设计**: 清晰的类和接口分离
- **事件驱动**: 基于事件的交互机制
- **可扩展性**: 易于扩展和维护的架构
- **兼容性**: 保持与原有功能的兼容

## 📁 项目结构

```
src/
├── classes/           # 主要类文件
│   ├── PDFCompared.ts    # 主控制器类
│   └── PDFH5.ts          # PDF渲染引擎
├── types/             # 类型定义
│   └── index.ts          # 所有类型定义
├── utils/             # 工具类
│   ├── colorHelper.ts    # 颜色处理工具
│   ├── eventManager.ts   # 事件管理器
│   ├── jerryRect.ts      # 矩形绘制类
│   └── svgLine.ts        # SVG线条绘制类
├── main.ts            # 主入口文件
└── style.css          # 样式文件
```

## 🛠️ 安装和使用

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 类型检查

```bash
npm run type-check
```

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 💻 使用示例

### 基本使用

```typescript
import { initPDFCompare } from './src/main.js';
import type { CompareItem } from './src/types/index.js';

// 准备对比数据
const item: CompareItem = {
    srcName: "技术开发（委托）合同版.docx",
    targetName: "技术开发（委托）合同版（条款差异）.docx",
    srcPdf: "https://example.com/source.pdf",
    targetPdf: "https://example.com/target.pdf",
    srcPages: 8,
    targetPages: 8,
    objectId: "unique-object-id",
    revisions: [
        {
            type: 2, // 1=新增，2=删除，3=修改
            id: 1,
            srcRev: {
                startPos: 2347,
                endPos: 2348,
                content: "整",
                page: 4
            },
            targetRev: {
                startPos: 2347,
                endPos: 2347,
                content: "",
                page: 4
            }
        }
        // ... 更多差异项
    ]
};

// 初始化PDF对比
const pdfCompared = initPDFCompare(item);
```

### 直接使用类

```typescript
import { PDFCompared } from './src/classes/PDFCompared.js';
import type { PDFObject } from './src/types/index.js';

const pdfObject: PDFObject = {
    Container: "main_res",
    Rev: item
};

const pdfCompared = new PDFCompared(pdfObject);
```

## 🏗️ 核心架构

### 主要类

- **PDFCompared**: 主控制器类，管理整个对比流程
- **PDFH5**: PDF渲染引擎，处理单个PDF的显示和交互
- **EventManager**: 事件管理器，处理各种交互事件
- **ColorHelper**: 颜色处理工具类
- **JerryRect**: 矩形绘制类，用于绘制差异高亮
- **SvgLine**: SVG线条绘制类，用于连接差异项

### 主要接口

- **CompareItem**: 对比数据的主要接口
- **Revision**: 单个差异项的接口
- **PDFOptions**: PDF渲染选项接口
- **RectOptions**: 矩形绘制选项接口

## 🔧 配置说明

### TypeScript配置

项目使用严格的TypeScript配置，包括：

- 严格类型检查
- 未使用变量检查
- 模块解析优化
- ES2022目标版本

### 依赖说明

#### 生产依赖
- `jquery`: DOM操作和事件处理
- `@types/jquery`: jQuery类型定义

#### 开发依赖
- `typescript`: TypeScript编译器
- `vite`: 构建工具
- `@types/fabric`: Fabric.js类型定义
- `@types/pdfjs-dist`: PDF.js类型定义

## 🎯 差异类型

系统支持三种差异类型：

1. **新增** (type: 1) - 蓝色高亮 `#4083f7`
2. **删除** (type: 2) - 红色高亮 `#fe2027`
3. **修改** (type: 3) - 橙色高亮 `#eca226`

## 📋 数据格式

### 对比数据结构

```typescript
interface CompareItem {
  srcName: string;           // 源文件名
  targetName: string;        // 目标文件名
  srcPdf: string;           // 源PDF URL
  targetPdf: string;        // 目标PDF URL
  srcPages: number;         // 源文件页数
  targetPages: number;      // 目标文件页数
  objectId: string;         // 唯一标识符
  revisions: Revision[];    // 差异数组
}
```

### 差异项结构

```typescript
interface Revision {
  type: 1 | 2 | 3;         // 差异类型
  id: number;              // 差异ID
  srcRev: RevisionItem;    // 源文件差异信息
  targetRev: RevisionItem; // 目标文件差异信息
}

interface RevisionItem {
  startPos: number;        // 起始位置
  endPos: number;          // 结束位置
  content: string;         // 文本内容
  page: number;            // 页码
}
```

## 🔍 功能特性

- **同步滚动**: 两个PDF可以同步滚动
- **差异高亮**: 自动高亮显示文档差异
- **交互选择**: 点击差异项进行高亮和定位
- **SVG连线**: 在差异项之间绘制连接线
- **差异列表**: 侧边栏显示所有差异的详细信息
- **全屏模式**: 支持全屏查看
- **响应式设计**: 适配不同屏幕尺寸

## 🐛 已知问题

1. 需要外部PDF.js库支持
2. 需要Fabric.js用于Canvas操作
3. 需要Snap.svg用于SVG绘制
4. 需要jQuery用于DOM操作

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您有任何问题或建议，请创建 Issue 或联系开发团队。
