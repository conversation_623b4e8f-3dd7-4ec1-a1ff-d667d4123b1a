// PDFCompared TypeScript 版本

import type {
  PDFObject,
  Revision,
  PDFOptions,
  DiffItem,
  SvgLineOptions,
  ISvgLine,
  RevisionType
} from '@/types';
import { PDFH5 } from './PDFH5';
import {
  TYPE_COLOURS,
  SELECTORS,
  CSS_CLASSES,
  EVENTS,
  DEFAULT_CONFIG
} from '../constants/index.js';

/**
 * PDF对比主控制器类
 * 负责管理两个PDF的对比、同步滚动、差异高亮等功能
 */
export class PDFCompared {
  private readonly PDF_DEBUG: boolean = false;
  private readonly typeColour: readonly string[] = TYPE_COLOURS;
  private readonly srcDom: string = SELECTORS.SRC_CONTAINER;
  private readonly targetDom: string = SELECTORS.TARGET_CONTAINER;

  // 状态管理
  private readonly pageMap = new Map<number, Revision[]>();
  private ctrPage: number = 1;
  private _selectedId: number | null = null;
  private synchro: boolean = true;
  private reactList: Record<number, Revision[]> = {};

  // 滚动状态
  private srcPdfScrollOver: boolean = false;
  private targetPdfScrollOver: boolean = false;
  private targetPdfScroll: boolean = false;
  private srcPdfScroll: boolean = false;

  // 渲染状态
  private isClassic: boolean = true;
  private isRenderSvg: boolean = false;

  // 组件实例
  private srcPdf!: PDFH5;
  private targetPdf!: PDFH5;
  private svgLine!: ISvgLine;

  // 完成状态
  private srcPdfComplete: boolean = false;
  private targetPdfComplete: boolean = false;

  constructor(pdfObject: PDFObject) {
    this.validatePdfObject(pdfObject);

    if (this.PDF_DEBUG) {
      // 调试模式下使用本地文件
      (pdfObject.Rev as any).srcPdf = "../static/srcPdf.pdf";
      (pdfObject.Rev as any).targetPdf = "../static/targetPdf.pdf";
    }

    this.init(pdfObject);
  }

  /**
   * 验证PDF对象参数
   */
  private validatePdfObject(pdfObject: PDFObject): void {
    if (!pdfObject || !pdfObject.Rev) {
      throw new Error('Invalid PDF object: missing Rev property');
    }

    const { Rev } = pdfObject;
    if (!Rev.srcPdf || !Rev.targetPdf) {
      throw new Error('Invalid PDF object: missing srcPdf or targetPdf');
    }

    if (!Array.isArray(Rev.revisions)) {
      throw new Error('Invalid PDF object: revisions must be an array');
    }
  }

  /**
   * 初始化PDF对比系统
   */
  private init(pdfObject: PDFObject): void {
    try {
      // 初始化SVG线条绘制器
      this.initSvgLine();

      // 创建PDF选项并初始化PDF实例
      const revObject = this.createPDFOptions(pdfObject);
      this.srcPdf = new PDFH5(this.srcDom, revObject.srcRev);
      this.targetPdf = new PDFH5(this.targetDom, revObject.targetRev);

      // 创建页面选项
      this.createPageOptions([...pdfObject.Rev.revisions]);

      // 初始化UI和事件
      this.initUI(pdfObject);
      this.initEvent();
    } catch (error) {
      console.error('Failed to initialize PDF comparison:', error);
      throw error;
    }
  }

  /**
   * 初始化SVG线条绘制器
   */
  private initSvgLine(): void {
    if (typeof (window as any).svgLine !== 'function') {
      throw new Error('svgLine is not available. Please ensure the SVG library is loaded.');
    }

    this.svgLine = new (window as any).svgLine();
    this.svgLine.initSvgContainer("svg-container");
  }

  /**
   * 创建监听
   */
  private initEvent(): void {
    this.pdfEvent();
    this.scrollEvent();
  }

  /**
   * 初始化PDF相关事件监听
   */
  private pdfEvent(): void {
    // 选中差异块事件
    this.srcPdf.off(EVENTS.SELECTED_RECT);
    this.srcPdf.on(EVENTS.SELECTED_RECT, (selectedId: number) => {
      this.selectedId = selectedId;
      this.targetPdf.setSelected(this.selectedId);
    });

    this.targetPdf.off(EVENTS.SELECTED_RECT);
    this.targetPdf.on(EVENTS.SELECTED_RECT, (selectedId: number) => {
      this.selectedId = selectedId;
      this.srcPdf.setSelected(this.selectedId);
    });

    // 放大缩小销毁
    this.srcPdf.off("destroy");
    this.srcPdf.on("destroy", () => {
      this.scrollEvent();
    });
    
    this.srcPdf.off("success");
    this.srcPdf.on("success", () => {
      this.createPageBar(this.ctrPage);
    });

    // 选中滚动结束
    this.srcPdf.off("selected_rect_scroll");
    this.srcPdf.on("selected_rect_scroll", () => {
      this.srcPdfScrollOver = true;
      this.renderSVG();
    });

    this.targetPdf.off("selected_rect_scroll");
    this.targetPdf.on("selected_rect_scroll", () => {
      this.targetPdfScrollOver = true;
      this.renderSVG();
    });

    // 页面变化
    this.srcPdf.off("pageChange");
    this.srcPdf.on("pageChange", (pageNum: number) => {
      this.ctrPage = Number(pageNum);
      this.createPageBar(Number(pageNum));
    });

    // 监听渲染完成
    this.srcPdf.off("complete");
    this.srcPdf.on("complete", () => {
      const $dom = $(this.srcDom);
      const dom = $dom[0];
      console.log(dom.offsetWidth);
      console.log(dom.scrollWidth);
      dom.scrollTo((dom.scrollWidth - dom.offsetWidth) / 2, 0);
      this.srcPdfComplete = true;
      this.pdfAllComplete("allPDFComplete");
    });

    this.targetPdf.off("complete");
    this.targetPdf.on("complete", () => {
      const $dom = $(this.targetDom);
      const dom = $dom[0];
      console.log(dom.offsetWidth);
      console.log(dom.scrollWidth);
      dom.scrollTo((dom.scrollWidth - dom.offsetWidth) / 2, 0);
      this.targetPdfComplete = true;
      this.pdfAllComplete("allPDFComplete");
    });
  }

  /**
   * PDF全部完成
   */
  private pdfAllComplete(messageData: string): void {
    if (this.srcPdfComplete && this.targetPdfComplete) {
      const targetWindow = window.parent;
      targetWindow.postMessage({
        data: messageData
      }, "*");
    }
  }

  /**
   * 滚动监听
   */
  private scrollEvent(): void {
    this.srcPdf.off("scroll");
    this.srcPdf.on("scroll", (scrollTop: number) => {
      if (!this.isRenderSvg) {
        this.svgLine.removeActiveLink();
      }
      if (this.targetPdfScroll && this.synchro && !this.isRenderSvg) {
        this.targetPdf.scroll(scrollTop);
      }
    });

    this.targetPdf.off("scroll");
    this.targetPdf.on("scroll", (scrollTop: number) => {
      if (!this.isRenderSvg) {
        this.svgLine.removeActiveLink();
      }
      if (this.srcPdfScroll && this.synchro && !this.isRenderSvg) {
        this.srcPdf.scroll(scrollTop);
      }
    });

    // 监听同步滚动
    $(this.srcDom).off("mouseover mouseout").on("mouseover mouseout", (e: JQuery.TriggeredEvent) => {
      const eventType = e.type;
      if (eventType === 'mouseover') {
        this.targetPdfScroll = true;
      } else {
        this.targetPdfScroll = false;
      }
    });

    $(this.targetDom).off("mouseover mouseout").on("mouseover mouseout", (e: JQuery.TriggeredEvent) => {
      const eventType = e.type;
      if (eventType === 'mouseover') {
        this.srcPdfScroll = true;
      } else {
        this.srcPdfScroll = false;
      }
    });
  }

  /**
   * 绘制SVG
   */
  private renderSVG(): void {
    this.pdfSelectedRect(this.selectedId);
    
    if (this.isClassic) {
      if (this.srcPdfScrollOver && this.targetPdfScrollOver) {
        const $differentnr = $(".differentnr");
        const element = $("#differentnrList .on").eq(0);

        if (element.length > 0) {
          // 获取目标元素与容器的偏移量
          const elementOffset = element.offset()!.top;
          const containerOffset = $differentnr.offset()!.top;
          const scrollTop = $differentnr.scrollTop()! + (elementOffset - containerOffset);

          // 滚动容器，使目标元素到顶部
          $differentnr.animate({ scrollTop: scrollTop }, 300);
          this.srcPdfScrollOver = false;
          this.targetPdfScrollOver = false;
          this.isRenderSvg = false;
          this.scrollEvent();
        }
      }
      return;
    }

    if (this.srcPdfScrollOver && this.targetPdfScrollOver) {
      if (this.selectedId) {
        const srcRect = this.srcPdf.getFabricRect(this.selectedId);
        const targetRect = this.targetPdf.getFabricRect(this.selectedId);
        const scrContainer = $(this.srcDom + " .viewerContainer");
        const targetContainer = $(this.targetDom + " .viewerContainer");
        const element = $("#normalDiffList .on").eq(0);
        const elementTop = element.offset()!.top - 20;
        const elementLeft = element.offset()!.left;
        const $middle = $("#normalDiffList");

        const srcPostObject: SvgLineOptions = {
          btnType: srcRect.stroke.colorHex(),
          startLeft: srcRect.left,
          startTop: srcRect.LastPageTop - scrContainer.scrollTop()!,
          midLeft: $(".srcContainer").outerWidth(true)!,
          midTop: srcRect.LastPageTop - scrContainer.scrollTop()!,
          endLeft: elementLeft,
          endTop: elementTop - 50,
          linkDirection: 0,
          linkStatus: 0
        };
        this.svgLine.drawPolyLine(srcPostObject);

        const targetPostObject: SvgLineOptions = {
          btnType: targetRect.stroke.colorHex(),
          startLeft: $(".srcContainer").outerWidth(true)! + $middle.outerWidth(true)! + targetRect.left,
          startTop: targetRect.LastPageTop - targetContainer.scrollTop()!,
          midLeft: $(".srcContainer").outerWidth(true)! + $middle.outerWidth(true)!,
          midTop: targetRect.LastPageTop - targetContainer.scrollTop()!,
          endLeft: elementLeft + $middle.outerWidth(true)!,
          endTop: elementTop - 50,
          linkDirection: 1,
          linkStatus: 0
        };
        this.svgLine.drawPolyLine(targetPostObject);
      }
      
      this.srcPdfScrollOver = false;
      this.targetPdfScrollOver = false;
      
      setTimeout(() => {
        this.isRenderSvg = false;
      }, 300);
      
      this.scrollEvent();
    }
  }

  /**
   * UI绑定事件
   */
  private initUI(pdfObject: PDFObject): void {
    // 名称
    $("#srcContainerPDFName").text(pdfObject.Rev.srcName);
    $("#targetContainerPDFName").text(pdfObject.Rev.targetName);

    // 差异
    $("#diffTotal").text(pdfObject.Rev.revisions.length);
    $("#classicDiffTotal").text(pdfObject.Rev.revisions.length);

    // 显示宽
    $(".o_zk").bind("click", () => {
      $(".classicWrapper_showmain").show();
      $(".simple-content").hide();
      $(".simpleWrapper").css("width", "200px");
      this.isClassic = true;
      this.svgLine.removeActiveLink();
    });

    // 显示窄
    $(".o_sq").bind("click", () => {
      $(".classicWrapper_showmain").hide();
      $(".simple-content").show(() => {
        this.createPageBar(this.ctrPage);
      });
      $(".simpleWrapper").css("width", "100px");
      this.isClassic = false;
      this.svgLine.removeActiveLink();
    });

    // 自动滚动
    $(".autoScroll").bind("click", () => {
      $(".autoScroll").toggleClass("on");
      this.synchro = !this.synchro;
    });
  }



  /**
   * 创建PDF数据
   */
  private createPDFOptions(pdfObject: PDFObject): {
    srcRev: PDFOptions;
    targetRev: PDFOptions;
  } {
    const _rev = pdfObject.Rev;
    const revisions = _rev.revisions;
    const _length = revisions.length;
    const _srcRev: DiffItem[] = [];
    const _targetRev: DiffItem[] = [];

    for (let i = 0; i < _length; i++) {
      const revision = revisions[i];
      const id = revision.id;
      const type = revision.type;
      const targetRev = revision.targetRev;
      const typeColor = this.typeColour[revision.type - 1];

      const targetDiff: DiffItem = {
        ...targetRev,
        id,
        type,
        fillStyle: typeColor
      };

      const srcDiff: DiffItem = {
        ...revision.srcRev,
        id,
        type,
        fillStyle: typeColor
      };

      _srcRev.push(srcDiff);
      _targetRev.push(targetDiff);
    }

    return {
      srcRev: {
        pdfurl: _rev.srcPdf,
        isDifference: true,
        pagesNum: _rev.srcPages,
        PDF_DEBUG: this.PDF_DEBUG,
        diffArray: _srcRev
      },
      targetRev: {
        pdfurl: _rev.targetPdf,
        isDifference: true,
        pagesNum: _rev.targetPages,
        PDF_DEBUG: this.PDF_DEBUG,
        diffArray: _targetRev
      }
    };
  }

  /**
   * 创建页面中间导航数据栏数据
   */
  private createPageOptions(revs: Revision[]): void {
    let _classHtml = "";

    for (let i = 0; i < revs.length; i++) {
      const rec = revs[i];
      let pageArray = this.pageMap.get(rec.srcRev.page);

      if (pageArray) {
        pageArray.push(rec);
      } else {
        pageArray = [rec];
      }

      this.pageMap.set(rec.srcRev.page, pageArray);
      _classHtml += this.createClassicBar(rec);
    }

    $("#differentnrList").html(_classHtml);

    // 绑定点击事件
    $(".differentnr_child").off("click").on("click", (evt: JQuery.ClickEvent) => {
      const currentTarget = evt.currentTarget;
      $(currentTarget).addClass("on").siblings().removeClass("on");
      this.selectedId = Number($(currentTarget).attr("data-id"));
      this.srcPdf.setSelected(this.selectedId);
      this.targetPdf.setSelected(this.selectedId);
    });
  }

  /**
   * 创建导航页
   */
  private createPageBar(page: number): void {
    this.reactList = {};
    const canvas0 = $(this.srcDom).find(".pageContainer" + page).find(".canvasImg" + page)[0];
    const $normalDiffList = $("#normalDiffList");
    const zoomRatio = ($normalDiffList.height()! - 20) / $(canvas0).height()!;

    const pageRectArray = this.pageMap.get(page);

    let _normalHtml = "";
    if (!pageRectArray || pageRectArray.length <= 0) {
      $("#normalDiffList").html(_normalHtml);
      return;
    }

    for (let i = 0; i < pageRectArray.length; i++) {
      const rect = pageRectArray[i];
      this.createNormalBarData(rect, zoomRatio);
    }

    _normalHtml = this.createNormalBar();
    $("#normalDiffList").html(_normalHtml);

    $(".dffBtn").off("click").on("click", (evt: JQuery.ClickEvent) => {
      $(".diff-row").removeClass("on");
      $(evt.currentTarget).addClass("on");

      if (!$(evt.currentTarget).hasClass("dffGroupBtn")) {
        $(".ant-groups").hide();
      }

      const id = Number($(evt.currentTarget).attr("data-id"));
      this.selectedId = id;
      this.srcPdf.setSelected(id);
      this.targetPdf.setSelected(id);
    });

    $(".btn-all").off("click").on("click", function() {
      $(this).next().show();
    });
  }

  /**
   * 创建经典导航
   */
  private createClassicBar(_rev: Revision): string {
    let _html = "";
    const _id = _rev.id;
    const _type = _rev.type;
    let _iconClass: string;
    let _iconName: string;

    if (_type === 2) {
      _iconClass = "sc";
      _iconName = "删除";
    } else if (_type === 3) {
      _iconClass = "xg";
      _iconName = "修改";
    } else if (_type === 1) {
      _iconClass = "xz";
      _iconName = "新增";
    } else {
      _iconClass = "";
      _iconName = "";
    }

    if (this.selectedId === _id) {
      _iconClass += " on";
    }

    const _srcText = _rev.srcRev.content;
    const _targetText = _rev.targetRev.content;

    _html += `<div data-id="${_id}" class="differentnr_child ${_iconClass}">`;
    _html += `<h4>${_iconName}</h4>`;
    _html += `<div class="diff_main">`;
    _html += `<p><label>左：</label><span>${_srcText}</span></p>`;
    _html += `<p><label>右：</label><span>${_targetText}</span></p>`;
    _html += `</div></div>`;

    return _html;
  }

  /**
   * 创建正常bar数据
   */
  private createNormalBarData(rect: Revision, zoomRatio: number): void {
    const _id = rect.id;
    const fabricRect = this.srcPdf.getFabricRect(_id);
    const top = fabricRect.top;

    (rect as any).y = top * zoomRatio;

    if (this.reactList[top]) {
      this.reactList[top].push(rect);
    } else {
      this.reactList[top] = [rect];
    }
  }

  /**
   * 创建正常导航
   */
  private createNormalBar(): string {
    let listHtml = "";

    for (const i in this.reactList) {
      const rectArray = this.reactList[i];
      listHtml += this.buildListOne(rectArray);
    }

    return listHtml;
  }

  /**
   * 创建一条数据
   */
  private buildListOne(rects: Revision[]): string {
    let listHtml: string;
    let className = "";

    if (rects.length === 1) {
      const type = rects[0].type;
      const id = rects[0].id;
      const page = rects[0].srcRev.page;
      const y = (rects[0] as any).y;

      if (id === this.selectedId) {
        className = "on";
      } else {
        className = "";
      }

      if (type === 1) {
        listHtml = `<div style="top:${y}px" class="diff-row dffBtn ${className}" data-id="${id}" data-src-page="${page}"><div class="btn-show btn-add"><i></i></div></div>`;
      } else if (type === 2) {
        listHtml = `<div style="top:${y}px" class="diff-row dffBtn ${className}" data-id="${id}" data-src-page="${page}"><div class="btn-show btn-sc"><i></i></div></div>`;
      } else if (type === 3) {
        listHtml = `<div style="top:${y}px" class="diff-row dffBtn ${className}" data-id="${id}" data-src-page="${page}"><div class="btn-show btn-xg"><i></i></div></div>`;
      } else {
        listHtml = "";
      }
    } else {
      let listHtml2 = '';
      let rowClass = "";
      let rowStyle = "";
      let rectsY = 0;

      for (let i = 0; i < rects.length; i++) {
        const rectObject = rects[i];
        const _type = rectObject.type;
        const _id = rectObject.id;
        const _page = rectObject.srcRev.page;
        rectsY = (rects[0] as any).y;

        if (_id === this.selectedId) {
          className = "on";
          rowClass = "on";
          rowStyle = 'style="display: block;"';
        } else {
          className = "";
        }

        if (_type === 1) {
          listHtml2 += `<div class="diff-row dffBtn dffGroupBtn ${className}" data-id="${_id}" data-src-page="${_page}"><div class="btn-show btn-add"><i></i></div></div>`;
        } else if (_type === 2) {
          listHtml2 += `<div class="diff-row dffBtn dffGroupBtn ${className}" data-id="${_id}" data-src-page="${_page}"><div class="btn-show btn-sc"><i></i></div></div>`;
        } else if (_type === 3) {
          listHtml2 += `<div class="diff-row dffBtn dffGroupBtn ${className}" data-id="${_id}" data-src-page="${_page}"><div class="btn-show btn-xg"><i></i></div></div>`;
        }
      }

      listHtml2 += '</div></div>';
      const isSelectOn = `<div class="diff-row ${rowClass}" style="top:${rectsY}px"><div class="btn-show btn-all"><i></i>(${rects.length})</div><div class="ant-groups" ${rowStyle}>`;
      listHtml = isSelectOn + listHtml2;
    }

    return listHtml;
  }

  /**
   * PDF选中的差异块
   */
  private pdfSelectedRect(id: number | null): void {
    let $diffListChild: JQuery;

    if (this.isClassic) {
      $diffListChild = $("#differentnrList").children();

      for (let i = 0; i < $diffListChild.length; i++) {
        const _el = $diffListChild[i];
        const _id = $(_el).attr("data-id");

        if (_id) {
          if (Number(_id) === id) {
            $(_el).addClass("on");
          } else {
            $(_el).removeClass("on");
          }
        } else {
          $(_el).removeClass("on");
        }
      }
    } else {
      $(".dffBtn").removeClass("on");
      $diffListChild = $("#normalDiffList").children();

      for (let i = 0; i < $diffListChild.length; i++) {
        const _el = $diffListChild[i];
        const _id = $(_el).attr("data-id");

        if (_id) {
          if (Number(_id) === id) {
            $(_el).addClass("on");
            break;
          }
        } else {
          const groups = $(_el).children('.ant-groups').children();

          for (let a = 0; a < groups.length; a++) {
            const _el2 = groups[a];
            const _id2 = $(_el2).attr("data-id");

            if (Number(_id2) === id) {
              $(_el2).addClass("on");
              $(_el2).parent().css("display", "block");
              break;
            }
          }
        }
      }
    }
  }

  /**
   * 设置选中ID的getter和setter
   */
  get selectedId(): number | null {
    return this._selectedId;
  }

  set selectedId(id: number | null) {
    this._selectedId = id;
    this.targetPdfScroll = false;
    this.srcPdfScroll = false;
    this.isRenderSvg = true;
    this.srcPdf.off("scroll");
    this.targetPdf.off("scroll");
    $(this.srcDom).off("mouseover mouseout");
    $(this.targetDom).off("mouseover mouseout");
    $("#diffNumber").text(id?.toString() || "0");
  }
}
