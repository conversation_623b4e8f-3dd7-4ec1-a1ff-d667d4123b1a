// PDFH5 TypeScript 版本

import type { 
  PDFOptions, 
  PageCache, 
  JsonTextData, 
  TextItem, 
  DiffItem,
  FabricCanvas,
  FabricRect,
  RectOptions
} from '../types/index.js';
import { EventManager } from '../utils/eventManager.js';
import { JerryRect } from '../utils/jerryRect.js';
import { createColorHelper, hexToRgba } from '../utils/colorHelper.js';

export class PDFH5 extends EventManager {
  private dom: string;
  private container: JQuery;
  private options: PDFOptions;
  private thePDF: any = null;
  private totalNum: number | null = null;
  private pages: JQuery | null = null;
  private initTime: number = 0;
  private scale: number = 1;
  private currentNum: number = 1;
  private loadedCount: number = 0;
  private endTime: number = 0;
  private timer: number | null = null;
  private cache: Record<string, PageCache> = {};
  private jsonTextData: Record<number, JsonTextData> | null = null;
  private rectDataByPage: Record<number, RectOptions[]> | null = null;
  private fabricCanvasForPage: Map<number, FabricCanvas> = new Map();
  private rectForId: Map<number, FabricRect[]> = new Map();
  private scaledViewport: number;
  private docWidth: number = 0;

  // DOM 元素
  private viewer: JQuery | null = null;
  private viewerContainer: JQuery | null = null;
  private pageNum: JQuery | null = null;
  private pageNow: JQuery | null = null;
  private pageTotal: JQuery | null = null;
  private loadingBar: JQuery | null = null;
  private progress: JQuery | null = null;
  private backTop: JQuery | null = null;
  private loading: JQuery | null = null;
  private showPageNum: boolean = true;
  private pdfjsLibPromise: Promise<any> | null = null; // PDF.js库加载Promise
  private resizeEvent: boolean = false; // 窗口大小改变事件标志

  constructor(dom: string, options: PDFOptions) {
    super();
    this.dom = dom;
    this.container = $(dom);
    this.options = options;
    this.scaledViewport = $(window).width()! <= 1440 ? 1.3 : 2;
    this.init(options);
  }

  /**
   * 初始化
   */
  private init(options: PDFOptions): void {
    this.docWidth = this.container.width()!;
    
    if ((this.container[0] as any).pdfLoaded) {
      this.destroy();
    }

    // 设置PDFJS配置
    if (options.cMapUrl) {
      window.PDFJS.cMapUrl = options.cMapUrl;
    } else {
      window.PDFJS.cMapUrl = 'http://data.shenht.com:9027/static/js/pdf-ie9/cmaps/';
    }
    window.PDFJS.cMapPacked = true;
    window.PDFJS.rangeChunkSize = 65536;

    (this.container[0] as any).pdfLoaded = false;
    this.container.addClass("pdfjs");
    this.initTime = new Date().getTime();

    // 触发初始滚动事件
    setTimeout(() => {
      this.emit("scroll", this.initTime);
    }, 0);

    // 设置默认选项
    this.setDefaultOptions();
    
    // 创建HTML结构
    this.createHTML();
    
    // 初始化DOM元素引用
    this.initDOMReferences();
    
    // 设置事件监听
    this.setupEventListeners();

    // 加载PDF
    this.loadPDF();
  }

  /**
   * 设置默认选项
   */
  private setDefaultOptions(): void {
    const defaults: Partial<PDFOptions> = {
      scale: this.scale,
      zoomEnable: true,
      scrollEnable: true,
      loadingBar: true,
      pageNum: true,
      backTop: false,
      URIenable: false,
      fullscreen: true,
      lazy: false,
      renderType: 'canvas',
      resize: true,
      textLayer: false,
      goto: 0,
      isDifference: false,
      diffArray: [],
      PDF_DEBUG: false,
      limit: 0,
      type: 'ajax'
    };

    this.options = { ...defaults, ...this.options };

    if (this.options.limit) {
      const n = parseFloat(this.options.limit.toString());
      (this.options as any).limit = isNaN(n) ? 0 : n < 0 ? 0 : n;
    } else {
      (this.options as any).limit = 0;
    }
  }

  /**
   * 创建HTML结构
   */
  private createHTML(): void {
    const html = `
      <div class="loadingBar">
        <div class="progress">
          <div class="glimmer"></div>
        </div>
      </div>
      <div class="pageNum">
        <div class="pageNum-bg"></div>
        <div class="pageNum-num">
          <span class="pageNow">1</span>/<span class="pageTotal">1</span>
        </div>
      </div>
      <div class="backTop"></div>
      <div class="loadEffect loading"></div>
    `;

    if (!this.container.find('.pageNum')[0]) {
      this.container.append(html);
    }

    const viewer = document.createElement("div");
    viewer.className = 'pdfViewer';
    const viewerContainer = document.createElement("div");
    viewerContainer.className = 'viewerContainer';
    viewerContainer.appendChild(viewer);
    this.container.append(viewerContainer);
  }

  /**
   * 初始化DOM元素引用
   */
  private initDOMReferences(): void {
    this.viewer = this.container.find('.pdfViewer');
    this.viewerContainer = this.container.find('.viewerContainer');
    this.pageNum = this.container.find('.pageNum');
    this.pageNow = this.pageNum!.find('.pageNow');
    this.pageTotal = this.pageNum!.find('.pageTotal');
    this.loadingBar = this.container.find('.loadingBar');
    this.progress = this.loadingBar!.find('.progress');
    this.backTop = this.container.find('.backTop');
    this.loading = this.container.find('.loading');
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.options.loadingBar) {
      this.loadingBar!.hide();
    }

    const containerH = this.container.height()!;
    const height = containerH * (1 / 5);

    // 监听容器大小发生改变
    this.viewerContainer!.resize(() => {
      // 处理操作 - 可以在这里添加resize逻辑
    });

    // 监听滚动
    this.viewerContainer!.off('scroll').on('scroll', () => {
      this.handleScroll(height, containerH);
    });
  }

  /**
   * 处理滚动事件
   */
  private handleScroll(height: number, containerH: number): void {
    const scrollTop = this.viewerContainer!.scrollTop()!;

    if (scrollTop >= 150) {
      if (this.options.backTop) {
        this.backTop!.show();
      }
    } else {
      if (this.options.backTop) {
        this.backTop!.fadeOut(200);
      }
    }

    if (this.viewerContainer) {
      this.pages = this.viewerContainer.find('.pageContainer');
    }

    clearTimeout(this.timer!);

    if (this.options.pageNum && this.pageNum && this.showPageNum) {
      this.pageNum.show();
    }

    if (this.pages) {
      this.pages.each((index: number, obj: HTMLElement) => {
        const top = obj.getBoundingClientRect().top;
        const bottom = obj.getBoundingClientRect().bottom;

        if (top <= height && bottom > height) {
          if (this.options.pageNum) {
            if (this.currentNum !== index + 1) {
              this.pageNow!.text(index + 1);
              this.currentNum = index + 1;
              this.emit("pageChange", this.currentNum);
            }
          }
        }

        if (top <= containerH && bottom > containerH) {
          // this.cacheNum = index + 1; // 移除未使用的变量
        }
      });
    }

    // 处理滚动到底部和顶部的情况
    if (scrollTop + this.container.height()! >= this.viewer![0].offsetHeight) {
      this.pageNow!.text(this.totalNum!);
      this.currentNum = this.totalNum!;
      this.emit("pageChange", this.currentNum);
    }

    if (scrollTop === 0) {
      this.pageNow!.text(1);
      this.currentNum = 1;
      this.emit("pageChange", this.currentNum);
    }

    this.timer = window.setTimeout(() => {
      if (this.options.pageNum && this.pageNum) {
        this.pageNum.fadeOut(200);
      }
    }, 1500);

    this.emit("scroll", scrollTop, this.pageNow!.text());
  }

  /**
   * 加载PDF
   */
  private loadPDF(): void {
    // 读取所有页面的文字数据
    if (this.options.isDifference && !this.jsonTextData && !this.rectDataByPage) {
      this.jsonTextData = {};
      this.rectDataByPage = {};
      this.initAjax().then(() => {
        this.renderPDF();
      }).catch(() => {
        throw new Error("读取ajax数据出错!");
      });
      return;
    } else {
      this.renderPDF();
    }
  }

  /**
   * 渲染PDF
   */
  private renderPDF(): void {
    const url = this.options.pdfurl;
    
    if (this.options.loadingBar) {
      this.loadingBar!.show();
      this.progress!.css({ width: "3%" });
    }

    if (url) {
      this.renderPdfFromUrl(url);
    } else {
      const time = new Date().getTime();
      this.endTime = time - this.initTime;
      this.emit("complete", "error", "Expect options.pdfurl or options.data!", this.endTime);
      this.emit("error", "Expect options.pdfurl or options.data!", this.endTime);
      throw new Error("Expect options.pdfurl or options.data!");
    }
  }

  /**
   * 从URL渲染PDF
   */
  private renderPdfFromUrl(url: string): void {
    (this.container[0] as any).pdfLoaded = true;

    const obj: any = { url };

    // 设置各种选项
    if (this.options.httpHeaders) obj.httpHeaders = this.options.httpHeaders;
    if (this.options.withCredentials) obj.withCredentials = true;
    if (this.options.password) obj.password = this.options.password;
    if (this.options.stopAtErrors) obj.stopAtErrors = false;
    if (this.options.disableFontFace) obj.disableFontFace = true;
    if (this.options.disableRange) obj.disableRange = true;
    if (this.options.disableStream) obj.disableStream = true;
    if (this.options.disableAutoFetch) obj.disableAutoFetch = true;

    obj.cMapPacked = true;
    obj.rangeChunkSize = 65536;

    this.pdfjsLibPromise = window.PDFJS.getDocument(obj).then((pdf: any) => {
      this.loading!.hide();
      this.thePDF = pdf;
      this.totalNum = pdf.numPages;

      if (this.options.limit! > 0) {
        this.totalNum = this.options.limit!;
      }

      this.pageTotal!.text(this.totalNum || 0);

      let promise = Promise.resolve();
      const num = Math.floor(100 / this.totalNum!).toFixed(2);

      for (let i = 1; i <= this.totalNum!; i++) {
        this.cache[i.toString()] = {
          page: null,
          loaded: false,
          container: null,
          scaledViewport: null
        };

        promise = promise.then(((pageNum: number) => {
          return this.thePDF.getPage(pageNum).then((page: any) => {
            window.setTimeout(() => {
              if (this.options.goto) {
                if (pageNum === this.options.goto) {
                  this.goto(pageNum);
                }
              }
            }, 0);

            this.cache[pageNum.toString()].page = page;
            const viewport = page.getViewport(this.options.scale!);
            const scaledViewport = page.getViewport(this.scaledViewport);

            let container: HTMLElement;
            const existingDiv = this.container.find('.pageContainer' + pageNum)[0];

            if (!existingDiv) {
              container = this.createPageContainer(pageNum, scaledViewport, viewport);
            } else {
              container = existingDiv;
            }

            this.cache[pageNum.toString()].container = container;
            this.cache[pageNum.toString()].scaledViewport = scaledViewport;
            this.pages = this.viewerContainer!.find('.pageContainer');

            return this.renderCanvas(page, scaledViewport, pageNum, parseFloat(num), container);
          });
        }).bind(null, i));
      }
    }).catch((err: Error) => {
      this.loading!.hide();
      const time = new Date().getTime();
      this.endTime = time - this.initTime;
      this.emit("complete", "error", err.message, this.endTime);
      this.emit("error", err.message, this.endTime);
    });
  }

  /**
   * 创建页面容器
   */
  private createPageContainer(pageNum: number, scaledViewport: any, viewport: any): HTMLElement {
    const container = document.createElement('div');
    container.className = 'pageContainer pageContainer' + pageNum;
    container.setAttribute('name', 'page=' + pageNum);
    container.setAttribute('title', 'Page ' + pageNum);

    const loadEffect = document.createElement('div');
    loadEffect.className = 'loadEffect';
    container.appendChild(loadEffect);

    this.viewer![0].appendChild(container);

    if ((window as any).ActiveXObject || "ActiveXObject" in window) {
      $(container).css({
        'width': scaledViewport.width + 'px',
        "height": scaledViewport.height + 'px'
      }).attr("data-scale", viewport.width / viewport.height);
    } else {
      let h = $(container).width()! / (viewport.viewBox[2] / viewport.viewBox[3]);
      if (h > viewport.height) {
        h = viewport.height;
      }
      $(container).css({
        'max-width': scaledViewport.width * 2,
        "max-height": scaledViewport.height * 2
      }).attr("data-scale", viewport.width / viewport.height);
    }

    return container;
  }

  /**
   * 渲染单页Canvas
   */
  private renderCanvas(page: any, viewport: any, pageNum: number, num: number, container: HTMLElement): Promise<void> {
    const scale = (this.docWidth / viewport.width).toFixed(2);
    const canvas = document.createElement("canvas");
    const fabricCanvas = document.createElement("canvas");

    const context = canvas.getContext('2d')!;

    canvas.height = viewport.height;
    canvas.width = viewport.width;
    fabricCanvas.height = viewport.height;
    fabricCanvas.width = viewport.width;

    if (this.options.loadingBar) {
      this.progress!.css({
        width: num * this.loadedCount + "%"
      });
    }

    const renderObj: any = {
      canvasContext: context,
      viewport: viewport
    };

    if (this.options.background) {
      renderObj.background = "rgba(255, 255, 255, 0)";
    }

    return page.render(renderObj).then(() => {
      this.loadedCount++;

      canvas.className = "canvasImg" + pageNum;
      fabricCanvas.id = "fabricCanvas" + this.dom + pageNum;
      $(fabricCanvas).attr("data-page", pageNum);

      const existingCanvas = this.container.find(".pageContainer" + pageNum).find(".canvasImg" + pageNum)[0];
      if (container && !existingCanvas) {
        container.appendChild(canvas);
      }

      // 创建fabric canvas
      container.appendChild(fabricCanvas);
      const _fabricCanvas = new window.fabric.Canvas("fabricCanvas" + this.dom + pageNum) as FabricCanvas;

      // 设置fabric canvas样式
      $(".canvas-container").css({
        "position": "absolute",
        "top": "0px",
        "left": "0px",
        "width": "100%",
        "height": "100%"
      });
      $(".canvas-container > canvas").css({
        "width": "100%",
        "height": "100%"
      });

      _fabricCanvas.viewportscale = parseFloat(scale);
      _fabricCanvas.viewportWidth = viewport.width;
      this.fabricCanvasForPage.set(pageNum, _fabricCanvas);

      (container.children[0] as HTMLElement).style.display = "none";

      const time = new Date().getTime();
      this.emit("render", pageNum, time - this.initTime, container);

    }).then(() => {
      return page.getTextContent();
    }).then(() => {
      return page.getTextContent();
    }).then(() => {
      // 计算差异
      if (this.options.isDifference === true) {
        // 渲染差异数据
        return this.renderDifRect(page.pageIndex, viewport.scale);
      }
      return Promise.resolve();
    }).then(() => {
      // 渲染结束
      if (this.loadedCount === this.totalNum) {
        this.finalRender();
      }

      if (!this.options.textLayer) {
        return;
      }

      if ($(container).find(".textLayer")[0]) {
        return;
      }

      // 创建文本层（如果需要）
      const textLayerDiv = document.createElement('div');
      textLayerDiv.setAttribute('class', 'textLayer');
      container.appendChild(textLayerDiv);

      viewport.width = viewport.width * parseFloat(scale);
      viewport.height = viewport.height * parseFloat(scale);

      // 这里需要TextLayerBuilder的实现，暂时注释
      // const textLayer = new TextLayerBuilder({
      //   textLayerDiv: textLayerDiv,
      //   pageIndex: page.pageIndex,
      //   viewport: viewport
      // });
      // textLayer.setTextContent(textContent);
      // textLayer.render();
    });
  }

  /**
   * 最终渲染完成
   */
  private finalRender(): void {
    const time = new Date().getTime();

    if (this.options.loadingBar) {
      this.progress!.css({ width: "100%" });
    }

    window.setTimeout(() => {
      if (this.loadingBar) this.loadingBar.hide();
    }, 300);

    this.endTime = time - this.initTime;

    if (this.options.renderType === "svg") {
      if (this.totalNum !== 1) {
        this.cache[(this.totalNum! - 1).toString()].loaded = true;
      } else {
        this.cache["1"].loaded = true;
      }
    }

    this.emit("complete", "success", "pdf加载完成", this.endTime);
    this.emit("success", this.endTime);
    this.resizeEvent = true;
  }

  /**
   * 渲染差异矩形
   */
  private renderDifRect(pageIndex: number, scale: number): Promise<void> {
    const _fabricCanvas = this.fabricCanvasForPage.get(pageIndex + 1);

    return new Promise((resolve) => {
      const pageRectArray = this.rectDataByPage![pageIndex + 1];

      if (pageRectArray && pageRectArray.length > 0) {
        for (let i = 0; i < pageRectArray.length; i++) {
          const options = pageRectArray[i];
          options.scale = scale;

          const rect = new JerryRect(options);
          const fabricRect = new window.fabric.Rect({
            width: rect.getWidth(),
            height: rect.getHeight(),
            left: rect.getX() + 6,
            top: rect.getY() + 5,
            stroke: rect.getStrokeStyle(),
            fill: 'transparent',
            clipName: options.id,
            page: pageIndex + 1,
            strokeWidth: 1,
            selectable: false,
            id: options.id,
            hoverCursor: "pointer"
          }) as FabricRect;

          _fabricCanvas!.add(fabricRect);

          fabricRect.on('mousedown', (e: any) => {
            this.emit("selected_rect", e.target.id);
            this.setSelected(e.target.id);
          });

          if (this.rectForId.get(options.id)) {
            this.rectForId.get(options.id)!.push(fabricRect);
          } else {
            this.rectForId.set(options.id, [fabricRect]);
          }
        }
      }
      resolve();
    });
  }

  /**
   * 设置选中的块
   */
  setSelected(rectId: number): void {
    this.getVisiblePageToUnSelect();
    const _fabricRectArray = this.rectForId.get(rectId);

    if (_fabricRectArray && _fabricRectArray.length > 0) {
      for (let i = 0; i < _fabricRectArray.length; i++) {
        const _fabricRect = _fabricRectArray[i];

        if (i === 0) {
          const fabricCanvas = _fabricRect.canvas.lowerCanvasEl;
          const pageContainer = fabricCanvas.closest('.pageContainer');

          if (!pageContainer || !this.viewerContainer) return;

          const pageTop = pageContainer.offsetTop;
          const canvasDisplayRatio = fabricCanvas.clientHeight / fabricCanvas.height;
          const rectTop = _fabricRect.top * canvasDisplayRatio;

          const viewerHeight = this.viewerContainer.height()!;
          const offset = Math.floor(viewerHeight * 0.3);

          const targetPosition = pageTop + rectTop - offset;

          this.viewerContainer.animate({
            scrollTop: targetPosition
          }, {
            duration: 300,
            easing: 'swing',
            complete: () => {
              this.emit("selected_rect_scroll");
            }
          });
        }
        this.animateOpacity(_fabricRect);
      }
    }
  }

  /**
   * 获取矩形块
   */
  getFabricRect(rectId: number): any {
    const rects = this.rectForId.get(rectId);
    let firstRectObject: any;

    if (rects && rects.length > 0) {
      firstRectObject = {};
      const firstRect = rects[0];
      const viewportWidth = firstRect.canvas.viewportWidth;
      const srcScale = (this.container.width()! / viewportWidth).toFixed(2);

      firstRectObject.stroke = firstRect.stroke;
      firstRectObject.left = firstRect.left * parseFloat(srcScale);
      firstRectObject.top = firstRect.top * parseFloat(srcScale);
      firstRectObject.LastPageTop = firstRectObject.top + this.getRectLastPage(firstRect.page);
      firstRectObject.page = firstRect.page;
    }

    return firstRectObject;
  }

  /**
   * 获取方块之前页的高度
   */
  getRectLastPage(pageNum: number): number {
    let signHeight = 0;
    const marginBottom = 8;

    const containerRect = this.viewerContainer![0].getBoundingClientRect();

    for (let i = 0; i < this.pages!.length; i++) {
      const page = this.pages![i];
      const pageRect = page.getBoundingClientRect();

      if (pageNum - 1 === i) {
        break;
      }

      const pageTopOffset = pageRect.top - containerRect.top;
      signHeight += pageTopOffset + pageRect.height + marginBottom;
    }

    return signHeight;
  }

  /**
   * 清除选中
   */
  private getVisiblePageToUnSelect(): void {
    for (const value of this.rectForId.values()) {
      const _fabricRectArray = value;
      for (let i = 0; i < _fabricRectArray.length; i++) {
        const _fabricRect = _fabricRectArray[i];
        const canvas = _fabricRect.canvas;
        _fabricRect.set({ fill: "transparent" });
        canvas.renderAll();
      }
    }
  }

  /**
   * 闪烁方块
   */
  private animateOpacity(fabricRect: FabricRect): void {
    const color = hexToRgba(fabricRect.stroke.colorHex(), 0.2).rgba;
    const canvas = fabricRect.canvas;

    fabricRect.set({ fill: color });
    canvas.renderAll();

    window.setTimeout(() => {
      fabricRect.set({ fill: "transparent" });
      canvas.renderAll();

      fabricRect.animate('fill', "transparent", {
        onChange: canvas.renderAll.bind(canvas),
        duration: 300,
        easing: window.fabric.util.ease.easeInOutCubic,
        onComplete: () => {
          fabricRect.set({ fill: color });
          canvas.renderAll();
        }
      });
    }, 100);
  }

  /**
   * 跳转到指定页
   */
  goto(num: number): void {
    if (!isNaN(num)) {
      if (this.viewerContainer) {
        this.pages = this.viewerContainer.find('.pageContainer');

        if (this.pages) {
          let signHeight = 0;
          if (num - 1 > 0) {
            signHeight = this.pages[0].getBoundingClientRect().height;
          }

          this.viewerContainer.animate({
            scrollTop: signHeight * (num - 1) + 8 * num
          }, 300);
        }
      }
    }
  }

  /**
   * 设置滚动
   */
  scroll(scrollTop: number): void {
    this.viewerContainer!.scrollTop(scrollTop);
  }

  /**
   * 销毁
   */
  destroy(callback?: () => void): void {
    this.reset();

    if (this.thePDF) {
      this.thePDF.destroy();
      this.thePDF = null;
    }

    if (this.viewerContainer) {
      this.viewerContainer.off();
      this.viewerContainer.remove();
      this.viewerContainer = null;
    }

    if (this.container) {
      this.container.off();
      this.container.html('');
    }

    // 重置所有属性
    this.totalNum = null;
    this.pages = null;
    this.initTime = 0;
    this.endTime = 0;
    this.viewer = null;
    this.pageNum = null;
    this.pageNow = null;
    this.pageTotal = null;
    this.loadingBar = null;
    this.progress = null;
    this.loadedCount = 0;
    this.timer = null;
    this.jsonTextData = null;
    this.rectDataByPage = null;
    this.fabricCanvasForPage = new Map();
    this.rectForId = new Map();

    callback && callback.call(this);
    this.emit("destroy");
  }

  /**
   * 重置
   */
  private reset(callback?: () => void): void {
    if (this.viewerContainer) {
      this.viewerContainer.scrollTop(0);
    }
    this.docWidth = this.container.width()!;
    callback && callback.call(this);
    this.emit("reset");
  }

  /**
   * 初始化Ajax请求
   */
  private initAjax(): Promise<void> {
    return new Promise((resolve, reject) => {
      for (let i = 0; i < this.options.pagesNum!; i++) {
        $.ajax({
          type: "GET",
          url: this.getURL(this.options.pdfurl!, i),
          dataType: "json",
          success: (data: any) => {
            this.jsonTextData![data.info.page] = this.parsingJsonPage(data);
            if (Object.keys(this.jsonTextData!).length === this.options.pagesNum) {
              this.calculatePageText().then(() => {
                this.allPageRect().then(() => {
                  resolve();
                });
              });
            }
          },
          error: (returnValue: any) => {
            console.log(returnValue);
            reject();
          }
        });
      }
    });
  }

  /**
   * 计算每页的字数
   */
  private calculatePageText(): Promise<void> {
    return new Promise((resolve) => {
      const keysArray = Object.keys(this.jsonTextData!);
      let pileText = 0;

      for (let i = 0; i < keysArray.length; i++) {
        const pageData = this.jsonTextData![parseInt(keysArray[i])];
        const textIndex = pageData.textIndex;
        pileText += textIndex;
        pageData.pileText = pileText;
      }
      resolve();
    });
  }

  /**
   * 计算每页真实方块
   */
  private allPageRect(): Promise<void> {
    const pageKeyArray = Object.keys(this.jsonTextData!);
    const pageNum = pageKeyArray.length;

    for (let i = 1; i <= pageNum; i++) {
      const diffArray = this.differenceByPage(i);

      for (let a = 0; a < diffArray.length; a++) {
        const diffObject = diffArray[a];
        const startPos = diffObject.startPos;
        const endPos = diffObject.endPos;
        const typeColor = createColorHelper(diffObject.fillStyle);
        const id = diffObject.id;

        let rect: RectOptions;

        if (startPos === endPos) {
          // 一个字
          if (startPos - 1 > 0) {
            rect = this.getPostRect(startPos - 1, id, typeColor);
          } else {
            rect = this.getPostRect(startPos, id, typeColor);
          }
          rect.width = 12;
          this.setRectByPage(rect);
        } else {
          const _boundArray: RectOptions[] = [];

          for (let b = startPos; b < endPos; b++) {
            rect = this.getPostRect(b, id, typeColor);
            const _preBound = _boundArray[_boundArray.length - 1];

            if (_preBound && _preBound.y === rect.y) {
              _preBound.width = rect.x - _preBound.x + rect.width;
              if (_preBound.str && rect.str) {
                _preBound.str += rect.str;
              }
            } else {
              _boundArray.push(rect);
            }
          }
          this.setRectsByPage(_boundArray);
        }
      }
    }
    return Promise.resolve();
  }

  /**
   * 获取真实方块大小位置
   */
  private getPostRect(pos: number, id: number, textColor: any): RectOptions {
    const pageKeyArray = Object.keys(this.jsonTextData!);
    const pageNum = pageKeyArray.length;
    let recordingPage = -1;
    let previousPagesTextNum = 0;

    for (let i = 1; i <= pageNum; i++) {
      const textIndex = this.jsonTextData![i].pileText!;
      if (textIndex > pos) {
        recordingPage = i;
        break;
      }
    }

    const bound: any = {};
    if (recordingPage === -1) {
      return bound;
    }

    if (recordingPage !== 1) {
      previousPagesTextNum = this.jsonTextData![recordingPage - 1].pileText!;
    }

    const _textIndex = pos - previousPagesTextNum;
    const jsonObject = this.jsonTextData![recordingPage].content[_textIndex];

    try {
      bound.str = jsonObject.str;
      bound.width = jsonObject.width;
      bound.height = jsonObject.height;
      bound.x = jsonObject.x;
      bound.y = jsonObject.y;
      bound.id = id;
      bound.fillStyle = textColor;
      bound.page = recordingPage;
    } catch (e) {
      console.log(e);
    }

    return bound;
  }

  /**
   * 设置当前页的rect数据
   */
  private setRectByPage(bound: RectOptions): void {
    let rectArray = this.rectDataByPage![bound.page];
    if (rectArray) {
      rectArray.push(bound);
    } else {
      rectArray = [];
      rectArray.push(bound);
      this.rectDataByPage![bound.page] = rectArray;
    }
  }

  /**
   * 设置一组块到当前页
   */
  private setRectsByPage(bounds: RectOptions[]): void {
    for (let i = 0; i < bounds.length; i++) {
      this.setRectByPage(bounds[i]);
    }
  }

  /**
   * 解析JSON对象
   */
  private parsingJsonPage(jsonPageData: any): JsonTextData {
    const content = jsonPageData.content;
    const contentLength = Object.keys(content).length;
    const textArray: TextItem[] = [];
    let index = 0;

    if (contentLength !== 0) {
      content.forEach((text: any) => {
        const str = text.str;
        const _left = text.x;

        if (str.length > 1) {
          // 如果有多文本，拆解文字
          const singleTextSizeWidth = text.width / str.length;

          for (let i = 0; i < str.length; i++) {
            const _newStr = str.substr(i, 1);
            const newObject: TextItem = {
              str: _newStr,
              width: text.width,
              height: text.height,
              rotation: text.rotation,
              scale: text.scale,
              x: _left + singleTextSizeWidth * i,
              y: text.y
            };
            textArray.push(newObject);
            index++;
          }
        } else {
          index++;
          textArray.push(text);
        }
      });
    }

    return {
      info: jsonPageData.info,
      content: textArray,
      textIndex: index
    };
  }

  /**
   * 返回当前页的所有差异
   */
  private differenceByPage(page: number): DiffItem[] {
    const _len = this.options.diffArray!.length;
    const differenceArray: DiffItem[] = [];

    for (let i = 0; i < _len; i++) {
      const _rev = this.options.diffArray![i];
      const _page = _rev.page;
      if (page === _page) {
        differenceArray.push(_rev);
      }
    }
    return differenceArray;
  }

  /**
   * 获取URL
   */
  private getURL(url: string, index: number): string {
    if (this.options.PDF_DEBUG) {
      if (this.dom === "#targetContainer") {
        return "../static/page" + (index + 1) + ".json";
      } else {
        return "../static/srcpage" + (index + 1) + ".json";
      }
    } else {
      return url + "&page=" + (index + 1);
    }
  }

  /**
   * 获取PDF加载状态
   */
  getPdfLoadPromise(): Promise<any> | null {
    return this.pdfjsLibPromise;
  }

  /**
   * 获取窗口大小改变事件状态
   */
  getResizeEventStatus(): boolean {
    return this.resizeEvent;
  }
}
