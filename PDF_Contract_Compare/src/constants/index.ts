// PDF合同对比系统常量定义

import { RevisionType } from '../types/index.js';

/**
 * 差异类型颜色映射
 */
export const REVISION_COLORS = {
  [RevisionType.ADD]: '#4083f7',    // 新增 - 蓝色
  [RevisionType.DELETE]: '#fe2027', // 删除 - 红色
  [RevisionType.MODIFY]: '#eca226'  // 修改 - 橙色
} as const;

/**
 * 差异类型颜色数组（保持向后兼容）
 */
export const TYPE_COLOURS: readonly string[] = [
  REVISION_COLORS[RevisionType.ADD],
  REVISION_COLORS[RevisionType.DELETE], 
  REVISION_COLORS[RevisionType.MODIFY]
] as const;

/**
 * DOM选择器常量
 */
export const SELECTORS = {
  SRC_CONTAINER: '#srcContainer',
  TARGET_CONTAINER: '#targetContainer',
  VIEWER_CONTAINER: '.viewerContainer',
  PDF_VIEWER: '.pdfViewer',
  PAGE_CONTAINER: '.pageContainer',
  LOADING_BAR: '.loadingBar',
  PROGRESS: '.progress',
  PAGE_NUM: '.pageNum',
  PAGE_NOW: '.pageNow',
  PAGE_TOTAL: '.pageTotal',
  BACK_TOP: '.backTop',
  LOADING: '.loading',
  DIFFERENT_NR_CHILD: '.differentnr_child',
  DIFF_BTN: '.dffBtn',
  DIFF_ROW: '.diff-row',
  ANT_GROUPS: '.ant-groups',
  BTN_ALL: '.btn-all',
  AUTO_SCROLL: '.autoScroll'
} as const;

/**
 * CSS类名常量
 */
export const CSS_CLASSES = {
  PDFJS: 'pdfjs',
  ON: 'on',
  CANVAS_IMG: 'canvasImg',
  FABRIC_CANVAS: 'fabricCanvas',
  TEXT_LAYER: 'textLayer',
  LOAD_EFFECT: 'loadEffect',
  CANVAS_CONTAINER: 'canvas-container',
  GLIMMER: 'glimmer',
  DIFF_HIGHLIGHT_ADD: 'diff-highlight-add',
  DIFF_HIGHLIGHT_DELETE: 'diff-highlight-delete',
  DIFF_HIGHLIGHT_MODIFY: 'diff-highlight-modify'
} as const;

/**
 * 默认配置常量
 */
export const DEFAULT_CONFIG = {
  SCALE: 1,
  SCALED_VIEWPORT_SMALL: 1.3,
  SCALED_VIEWPORT_LARGE: 2,
  SCREEN_WIDTH_THRESHOLD: 1440,
  SCROLL_THRESHOLD: 150,
  ANIMATION_DURATION: 300,
  FADE_OUT_DURATION: 200,
  PAGE_NUM_HIDE_DELAY: 1500,
  ANIMATION_FPS: 60,
  RANGE_CHUNK_SIZE: 65536,
  PROGRESS_INITIAL: 3,
  PROGRESS_COMPLETE: 100,
  CONTAINER_HEIGHT_RATIO: 0.2,
  RECT_PADDING: {
    WIDTH_OFFSET: 4,
    HEIGHT_OFFSET: 6,
    HEIGHT_ADDITION: 12,
    FABRIC_LEFT_OFFSET: 6,
    FABRIC_TOP_OFFSET: 5
  }
} as const;

/**
 * 事件名称常量
 */
export const EVENTS = {
  SCROLL: 'scroll',
  PAGE_CHANGE: 'pageChange',
  SELECTED_RECT: 'selected_rect',
  SELECTED_RECT_SCROLL: 'selected_rect_scroll',
  COMPLETE: 'complete',
  SUCCESS: 'success',
  ERROR: 'error',
  RENDER: 'render',
  DESTROY: 'destroy',
  RESET: 'reset',
  RESIZE: 'resize',
  MOUSEOVER: 'mouseover',
  MOUSEOUT: 'mouseout',
  CLICK: 'click'
} as const;

/**
 * HTTP状态码常量
 */
export const HTTP_STATUS = {
  OK: 200,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
} as const;

/**
 * 文件类型常量
 */
export const FILE_TYPES = {
  PDF: 'application/pdf',
  JSON: 'application/json'
} as const;

/**
 * 错误消息常量
 */
export const ERROR_MESSAGES = {
  PDF_URL_OR_DATA_REQUIRED: 'Expect options.pdfurl or options.data!',
  AJAX_DATA_ERROR: '读取ajax数据出错!',
  SVG_CONTAINER_NOT_INITIALIZED: 'SVG容器未初始化',
  CANVAS_CONTEXT_NOT_FOUND: 'Canvas上下文未找到',
  PDF_LOAD_FAILED: 'PDF加载失败',
  RENDER_ERROR: 'JerryRect render error:'
} as const;

/**
 * 调试信息常量
 */
export const DEBUG_MESSAGES = {
  SVG_CONTAINER_INITIALIZED: 'SVG容器已初始化',
  PDF_LOADED: 'PDF加载完成',
  ANIMATION_STARTED: '动画已开始',
  ANIMATION_STOPPED: '动画已停止'
} as const;
