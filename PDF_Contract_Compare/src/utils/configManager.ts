// 配置管理器

import type { PDFOptions } from '../types/index.js';
import { DEFAULT_CONFIG } from '../constants/index.js';
import { deepClone, safeGet } from './helpers.js';

/**
 * 配置管理器类
 * 提供配置的合并、验证和管理功能
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private config: Record<string, any> = {};

  private constructor() {
    this.config = deepClone(DEFAULT_CONFIG);
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * 获取配置值
   * @param key 配置键，支持点分隔的嵌套键
   * @param defaultValue 默认值
   * @returns 配置值
   */
  get<T>(key: string, defaultValue?: T): T {
    return safeGet(this.config, key, defaultValue) as T;
  }

  /**
   * 设置配置值
   * @param key 配置键
   * @param value 配置值
   */
  set(key: string, value: any): void {
    const keys = key.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current) || typeof current[k] !== 'object') {
        current[k] = {};
      }
      current = current[k];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * 合并配置
   * @param newConfig 新配置对象
   */
  merge(newConfig: Record<string, any>): void {
    this.config = this.deepMerge(this.config, newConfig);
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = deepClone(target);
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (
          typeof source[key] === 'object' &&
          source[key] !== null &&
          !Array.isArray(source[key]) &&
          typeof result[key] === 'object' &&
          result[key] !== null &&
          !Array.isArray(result[key])
        ) {
          result[key] = this.deepMerge(result[key], source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }
    
    return result;
  }

  /**
   * 重置配置为默认值
   */
  reset(): void {
    this.config = deepClone(DEFAULT_CONFIG);
  }

  /**
   * 获取所有配置
   */
  getAll(): Record<string, any> {
    return deepClone(this.config);
  }

  /**
   * 验证PDF选项配置
   * @param options PDF选项
   * @returns 验证结果
   */
  validatePDFOptions(options: Partial<PDFOptions>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 验证必需的URL或数据
    if (!options.pdfurl && !options.data) {
      errors.push('Either pdfurl or data must be provided');
    }

    // 验证缩放比例
    if (options.scale !== undefined) {
      if (typeof options.scale !== 'number' || options.scale <= 0 || options.scale > 10) {
        errors.push('Scale must be a number between 0 and 10');
      }
    }

    // 验证页数限制
    if (options.limit !== undefined) {
      if (typeof options.limit !== 'number' || options.limit < 0) {
        errors.push('Limit must be a non-negative number');
      }
    }

    // 验证跳转页码
    if (options.goto !== undefined) {
      if (typeof options.goto !== 'number' || options.goto < 1) {
        errors.push('Goto must be a positive number');
      }
    }

    // 验证渲染类型
    if (options.renderType !== undefined) {
      if (!['svg', 'canvas'].includes(options.renderType)) {
        errors.push('RenderType must be either "svg" or "canvas"');
      }
    }

    // 验证请求类型
    if (options.type !== undefined) {
      if (!['fetch', 'ajax'].includes(options.type)) {
        errors.push('Type must be either "fetch" or "ajax"');
      }
    }

    // 验证HTTP头
    if (options.httpHeaders !== undefined) {
      if (typeof options.httpHeaders !== 'object' || options.httpHeaders === null) {
        errors.push('HttpHeaders must be an object');
      }
    }

    // 验证差异数组
    if (options.diffArray !== undefined) {
      if (!Array.isArray(options.diffArray)) {
        errors.push('DiffArray must be an array');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 创建默认PDF选项
   * @param overrides 覆盖选项
   * @returns 完整的PDF选项
   */
  createPDFOptions(overrides: Partial<PDFOptions> = {}): PDFOptions {
    const defaultOptions: PDFOptions = {
      scale: this.get('SCALE', 1),
      zoomEnable: true,
      scrollEnable: true,
      loadingBar: true,
      pageNum: true,
      backTop: false,
      URIenable: false,
      fullscreen: true,
      lazy: false,
      renderType: 'canvas',
      resize: true,
      textLayer: false,
      goto: 0,
      isDifference: false,
      diffArray: [],
      PDF_DEBUG: false,
      limit: 0,
      type: 'ajax'
    };

    return { ...defaultOptions, ...overrides };
  }

  /**
   * 获取环境相关配置
   */
  getEnvironmentConfig(): {
    isDevelopment: boolean;
    isProduction: boolean;
    isBrowser: boolean;
    userAgent: string;
  } {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isProduction = process.env.NODE_ENV === 'production';
    const isBrowser = typeof window !== 'undefined';
    const userAgent = isBrowser ? navigator.userAgent : 'Node.js';

    return {
      isDevelopment,
      isProduction,
      isBrowser,
      userAgent
    };
  }
}

// 导出单例实例
export const configManager = ConfigManager.getInstance();
