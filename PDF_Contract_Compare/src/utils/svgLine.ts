// SVG线条绘制工具类

import type { SvgLineOptions, ISvgLine } from '../types/index.js';

export class SvgLine implements ISvgLine {
  private svgContainer: any = null; // Snap.svg 实例
  private containerId: string = ''; // 保留用于调试和日志

  /**
   * 初始化SVG容器
   * @param containerId 容器ID
   */
  initSvgContainer(containerId: string): void {
    this.containerId = containerId;
    // 使用Snap.svg创建SVG容器
    this.svgContainer = (window as any).Snap(`#${containerId}`);

    // 使用containerId进行调试日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`SVG容器已初始化: ${this.containerId}`);
    }
  }

  /**
   * 绘制折线
   * @param options 绘制选项
   */
  drawPolyLine(options: SvgLineOptions): void {
    if (!this.svgContainer) {
      console.warn('SVG容器未初始化');
      return;
    }

    const {
      btnType,
      startLeft,
      startTop,
      midLeft,
      midTop,
      endLeft,
      endTop,
      linkDirection,
      linkStatus
    } = options;

    // 创建路径字符串
    const pathString = this.createPathString(
      startLeft,
      startTop,
      midLeft,
      midTop,
      endLeft,
      endTop,
      linkDirection
    );

    // 创建路径元素
    const path = this.svgContainer.path(pathString);
    
    // 设置样式
    path.attr({
      fill: 'none',
      stroke: btnType,
      strokeWidth: 2,
      strokeDasharray: linkStatus === 1 ? '5,5' : 'none',
      class: 'svg-link active-link'
    });

    // 添加动画效果
    this.addPathAnimation(path);
  }

  /**
   * 创建路径字符串
   */
  private createPathString(
    startLeft: number,
    startTop: number,
    midLeft: number,
    midTop: number,
    endLeft: number,
    endTop: number,
    linkDirection: number
  ): string {
    if (linkDirection === 0) {
      // 左侧连线
      return `M${startLeft},${startTop} L${midLeft},${midTop} L${endLeft},${endTop}`;
    } else {
      // 右侧连线
      return `M${startLeft},${startTop} L${midLeft},${midTop} L${endLeft},${endTop}`;
    }
  }

  /**
   * 添加路径动画
   */
  private addPathAnimation(path: any): void {
    const length = path.getTotalLength();
    
    path.attr({
      strokeDasharray: length + ' ' + length,
      strokeDashoffset: length
    });

    // 动画绘制
    path.animate({
      strokeDashoffset: 0
    }, 500, (window as any).mina.easeinout);
  }

  /**
   * 移除活动连线
   */
  removeActiveLink(): void {
    if (!this.svgContainer) return;
    
    // 移除所有活动连线
    const activeLinks = this.svgContainer.selectAll('.active-link');
    activeLinks.forEach((link: any) => {
      link.remove();
    });
  }

  /**
   * 清除所有连线
   */
  clearAllLinks(): void {
    if (!this.svgContainer) return;
    
    this.svgContainer.clear();
  }

  /**
   * 设置SVG容器尺寸
   */
  setSvgSize(width: number, height: number): void {
    if (!this.svgContainer) return;
    
    this.svgContainer.attr({
      width: width,
      height: height
    });
  }

  /**
   * 获取SVG容器
   */
  getSvgContainer(): any {
    return this.svgContainer;
  }

  /**
   * 销毁SVG容器
   */
  destroy(): void {
    if (this.svgContainer) {
      this.svgContainer.clear();
      this.svgContainer = null;
    }
  }
}

// 导出单例实例
export const svgLineInstance = new SvgLine();

// 兼容原有的全局变量方式
(window as any).svgLine = SvgLine;
