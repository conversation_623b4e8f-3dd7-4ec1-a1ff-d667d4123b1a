// 性能监控工具

import { isBrowser } from './helpers.js';

/**
 * 性能指标接口
 */
interface PerformanceMetric {
  readonly name: string;
  readonly startTime: number;
  readonly endTime?: number;
  readonly duration?: number;
  readonly metadata?: Record<string, any>;
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = true;

  private constructor() {
    this.isEnabled = isBrowser() && 'performance' in window;
  }

  /**
   * 获取单例实例
   */
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 开始性能测量
   * @param name 测量名称
   * @param metadata 元数据
   */
  start(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const startTime = this.now();
    this.metrics.set(name, {
      name,
      startTime,
      metadata
    });

    if (isBrowser() && 'mark' in performance) {
      performance.mark(`${name}-start`);
    }
  }

  /**
   * 结束性能测量
   * @param name 测量名称
   * @returns 测量结果
   */
  end(name: string): PerformanceMetric | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`);
      return null;
    }

    const endTime = this.now();
    const duration = endTime - metric.startTime;

    const completedMetric: PerformanceMetric = {
      ...metric,
      endTime,
      duration
    };

    this.metrics.set(name, completedMetric);

    if (isBrowser() && 'mark' in performance && 'measure' in performance) {
      performance.mark(`${name}-end`);
      try {
        performance.measure(name, `${name}-start`, `${name}-end`);
      } catch (error) {
        console.debug('Performance measure failed:', error);
      }
    }

    return completedMetric;
  }

  /**
   * 获取性能指标
   * @param name 测量名称
   * @returns 性能指标
   */
  getMetric(name: string): PerformanceMetric | null {
    return this.metrics.get(name) || null;
  }

  /**
   * 获取所有性能指标
   * @returns 所有性能指标
   */
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * 清除指定性能指标
   * @param name 测量名称
   */
  clear(name: string): void {
    this.metrics.delete(name);
    
    if (isBrowser() && 'clearMarks' in performance && 'clearMeasures' in performance) {
      performance.clearMarks(`${name}-start`);
      performance.clearMarks(`${name}-end`);
      performance.clearMeasures(name);
    }
  }

  /**
   * 清除所有性能指标
   */
  clearAll(): void {
    this.metrics.clear();
    
    if (isBrowser() && 'clearMarks' in performance && 'clearMeasures' in performance) {
      performance.clearMarks();
      performance.clearMeasures();
    }
  }

  /**
   * 获取当前时间戳
   */
  private now(): number {
    if (isBrowser() && 'performance' in window && 'now' in performance) {
      return performance.now();
    }
    return Date.now();
  }

  /**
   * 记录内存使用情况
   * @returns 内存信息
   */
  getMemoryInfo(): Record<string, number> | null {
    if (!isBrowser()) return null;

    const memory = (performance as any).memory;
    if (!memory) return null;

    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    };
  }

  /**
   * 生成性能报告
   * @returns 性能报告
   */
  generateReport(): {
    metrics: PerformanceMetric[];
    summary: {
      totalMetrics: number;
      completedMetrics: number;
      averageDuration: number;
      slowestMetric?: PerformanceMetric;
      fastestMetric?: PerformanceMetric;
    };
    memory?: Record<string, number>;
  } {
    const metrics = this.getAllMetrics();
    const completedMetrics = metrics.filter(m => m.duration !== undefined);
    
    let slowestMetric: PerformanceMetric | undefined;
    let fastestMetric: PerformanceMetric | undefined;
    let totalDuration = 0;

    for (const metric of completedMetrics) {
      if (metric.duration !== undefined) {
        totalDuration += metric.duration;
        
        if (!slowestMetric || metric.duration > (slowestMetric.duration || 0)) {
          slowestMetric = metric;
        }
        
        if (!fastestMetric || metric.duration < (fastestMetric.duration || Infinity)) {
          fastestMetric = metric;
        }
      }
    }

    const averageDuration = completedMetrics.length > 0 ? totalDuration / completedMetrics.length : 0;

    return {
      metrics,
      summary: {
        totalMetrics: metrics.length,
        completedMetrics: completedMetrics.length,
        averageDuration,
        slowestMetric,
        fastestMetric
      },
      memory: this.getMemoryInfo() || undefined
    };
  }

  /**
   * 启用性能监控
   */
  enable(): void {
    this.isEnabled = true;
  }

  /**
   * 禁用性能监控
   */
  disable(): void {
    this.isEnabled = false;
  }

  /**
   * 检查是否启用
   */
  get enabled(): boolean {
    return this.isEnabled;
  }
}

/**
 * 性能装饰器
 * @param name 测量名称
 * @returns 装饰器函数
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const measureName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      const monitor = PerformanceMonitor.getInstance();
      monitor.start(measureName);
      
      try {
        const result = originalMethod.apply(this, args);
        
        if (result instanceof Promise) {
          return result.finally(() => {
            monitor.end(measureName);
          });
        } else {
          monitor.end(measureName);
          return result;
        }
      } catch (error) {
        monitor.end(measureName);
        throw error;
      }
    };

    return descriptor;
  };
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();
