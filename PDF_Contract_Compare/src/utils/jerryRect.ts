// 矩形绘制类

import type { RectOptions } from '../types/index';
import { DEFAULT_CONFIG, ERROR_MESSAGES } from '../constants/index.js';
import { clamp } from './helpers.js';

/**
 * 矩形绘制类
 * 提供Canvas矩形绘制、动画和交互功能
 */
export class JerryRect {
  private Hz: number = 0;
  private readonly option: RectOptions;
  private fillStyle: string;
  private strokeStyle: string;
  private readonly lineWidth: number;
  private readonly scale: number;
  private rotation: number; // 保留用于未来的旋转功能
  private width: number;
  private height: number;
  private x: number;
  private y: number;
  private ctx?: CanvasRenderingContext2D;
  private animationId?: number | NodeJS.Timeout;

  constructor(option: RectOptions) {
    this.option = option;

    // 安全地处理fillStyle，确保它是ColorHelper对象
    if (option.fillStyle && typeof option.fillStyle.colorRgb === 'function') {
      this.fillStyle = option.fillStyle.colorRgb(0.5);
      this.strokeStyle = option.fillStyle.colorRgb();
    } else {
      // 如果不是ColorHelper对象，使用默认颜色
      this.fillStyle = "rgba(255, 0, 0, 0.5)";
      this.strokeStyle = "red";
    }

    this.lineWidth = clamp(option.lineWidth ?? 1, 0.1, 10);
    this.scale = clamp(option.scale ?? 1, 0.1, 10);
    this.rotation = option.rotation ?? 0;

    // 使用常量进行计算
    const { RECT_PADDING } = DEFAULT_CONFIG;
    this.width = Math.max(0, option.width * this.scale);
    this.height = Math.max(0, (option.height + RECT_PADDING.HEIGHT_ADDITION) * this.scale);
    this.x = (option.x - RECT_PADDING.WIDTH_OFFSET) * this.scale;
    this.y = (option.y - RECT_PADDING.HEIGHT_OFFSET - (option.height + RECT_PADDING.HEIGHT_ADDITION - RECT_PADDING.HEIGHT_OFFSET)) * this.scale;

    this.ctx = option.ctx || undefined;
  }

  /**
   * 渲染矩形
   * @throws {Error} 当Canvas上下文无效时抛出错误
   */
  render(): void {
    if (!this.ctx) {
      throw new Error(ERROR_MESSAGES.CANVAS_CONTEXT_NOT_FOUND);
    }

    if (!this.isValid()) {
      console.warn('Attempting to render invalid rectangle');
      return;
    }

    try {
      this.ctx.save();
      this.ctx.beginPath();
      this.ctx.lineWidth = this.lineWidth;
      this.ctx.rect(this.x, this.y, this.width, this.height);
      this.ctx.fillStyle = this.fillStyle;
      this.ctx.fill();
      this.ctx.strokeStyle = this.strokeStyle;
      this.ctx.stroke();
      this.ctx.restore();
    } catch (error) {
      console.warn(ERROR_MESSAGES.RENDER_ERROR, error);
      this.safeRestore();
      throw error;
    }
  }

  /**
   * 安全地恢复Canvas状态
   */
  private safeRestore(): void {
    try {
      this.ctx?.restore();
    } catch (error) {
      // 忽略restore错误，避免二次错误
      console.debug('Canvas restore failed:', error);
    }
  }

  /**
   * 动画渲染
   */
  animateRender(): void {
    if (!this.ctx) return;

    // 停止之前的动画
    this.stopAnimation();

    this.ctx.clearRect(this.x, this.y, this.width, this.height);
    this.ctx.closePath();

    // 使用requestAnimationFrame代替setTimeout以获得更好的性能
    const animate = () => {
      if (!this.ctx) return; // 检查ctx是否仍然存在

      this.Hz++;
      if (this.Hz > 1) {
        this.Hz = 0;
        this.render();
      }

      // 继续动画循环
      if (typeof requestAnimationFrame !== 'undefined') {
        this.animationId = requestAnimationFrame(animate);
      } else {
        this.animationId = window.setTimeout(animate, 1000 / 60) as any;
      }
    };

    animate();
  }

  /**
   * 停止动画
   */
  stopAnimation(): void {
    if (this.animationId !== undefined) {
      if (typeof this.animationId === 'number' && typeof cancelAnimationFrame !== 'undefined') {
        cancelAnimationFrame(this.animationId);
      } else {
        clearTimeout(this.animationId as NodeJS.Timeout);
      }
      (this as any).animationId = undefined;
    }
  }

  /**
   * 判断点是否在矩形内
   * @param x X坐标
   * @param y Y坐标
   * @returns 是否在矩形内
   */
  isPointInRect(x: number, y: number): boolean {
    return x >= this.x && 
           x <= this.x + this.width && 
           y >= this.y && 
           y <= this.y + this.height;
  }

  /**
   * 获取矩形边界
   */
  getBounds(): { x: number; y: number; width: number; height: number } {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    };
  }

  /**
   * 更新位置
   * @param x 新的X坐标
   * @param y 新的Y坐标
   */
  updatePosition(x: number, y: number): void {
    this.x = x;
    this.y = y;
  }

  /**
   * 更新尺寸
   * @param width 新的宽度
   * @param height 新的高度
   */
  updateSize(width: number, height: number): void {
    this.width = width;
    this.height = height;
  }

  /**
   * 销毁矩形
   */
  destroy(): void {
    // 停止动画
    this.stopAnimation();

    // 清除画布内容
    if (this.ctx) {
      this.ctx.clearRect(this.x, this.y, this.width, this.height);
    }

    // 清除引用
    (this as any).ctx = undefined;
  }

  /**
   * 获取宽度
   */
  getWidth(): number {
    return this.width;
  }

  /**
   * 获取高度
   */
  getHeight(): number {
    return this.height;
  }

  /**
   * 获取X坐标
   */
  getX(): number {
    return this.x;
  }

  /**
   * 获取Y坐标
   */
  getY(): number {
    return this.y;
  }

  /**
   * 获取描边样式
   */
  getStrokeStyle(): string {
    return this.strokeStyle;
  }

  /**
   * 获取填充样式
   */
  getFillStyle(): string {
    return this.fillStyle;
  }

  /**
   * 设置填充样式
   */
  setFillStyle(fillStyle: string): void {
    this.fillStyle = fillStyle;
  }

  /**
   * 设置描边样式
   */
  setStrokeStyle(strokeStyle: string): void {
    this.strokeStyle = strokeStyle;
  }

  /**
   * 获取选项对象
   */
  getOption(): RectOptions {
    return this.option;
  }

  /**
   * 检查矩形是否有效
   */
  isValid(): boolean {
    return this.width > 0 && this.height > 0 && this.ctx !== undefined;
  }

  /**
   * 克隆矩形
   */
  clone(): JerryRect {
    return new JerryRect({ ...this.option });
  }

  /**
   * 获取旋转角度
   */
  getRotation(): number {
    return this.rotation;
  }

  /**
   * 设置旋转角度
   */
  setRotation(rotation: number): void {
    this.rotation = rotation;
  }
}
