// 事件管理器

import type { EventCallback, EventType } from '../types/index.js';

/**
 * 事件管理器类
 * 提供类型安全的事件注册、移除和触发功能
 */
export class EventManager {
  private readonly eventType: EventType = {};

  /**
   * 注册事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   * @throws {Error} 当参数无效时抛出错误
   */
  on(type: string, callback: EventCallback): void {
    if (!type || typeof type !== 'string') {
      throw new Error('Event type must be a non-empty string');
    }
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    if (!this.eventType[type]) {
      this.eventType[type] = [];
    }
    this.eventType[type].push(callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型，如果不提供则移除所有事件
   * @param callback 可选的特定回调函数，如果提供则只移除该回调
   */
  off(type?: string, callback?: EventCallback): void {
    if (type === undefined) {
      // 清空所有事件
      this.clear();
      return;
    }

    if (!this.eventType[type]) {
      return;
    }

    if (callback) {
      // 移除特定回调
      const index = this.eventType[type].indexOf(callback);
      if (index > -1) {
        this.eventType[type].splice(index, 1);
      }
    } else {
      // 移除该类型的所有回调
      this.eventType[type] = [];
    }
  }

  /**
   * 触发事件
   * @param eventType 事件类型
   * @param args 参数
   * @returns 是否有监听器被触发
   */
  emit(eventType: string, ...args: any[]): boolean {
    if (!eventType || typeof eventType !== 'string') {
      console.warn('Invalid event type:', eventType);
      return false;
    }

    const callbacks = this.eventType[eventType];
    if (!callbacks || callbacks.length === 0) {
      return false;
    }

    let hasError = false;
    for (const callback of callbacks) {
      try {
        callback.call(this, ...args);
      } catch (error) {
        console.error(`Error in event callback for '${eventType}':`, error);
        hasError = true;
      }
    }

    return !hasError;
  }

  /**
   * 一次性事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  once(type: string, callback: EventCallback): void {
    const onceCallback: EventCallback = (...args: any[]) => {
      this.off(type, onceCallback);
      callback.call(this, ...args);
    };
    this.on(type, onceCallback);
  }

  /**
   * 获取指定事件类型的监听器数量
   * @param type 事件类型
   * @returns 监听器数量
   */
  listenerCount(type: string): number {
    return this.eventType[type]?.length ?? 0;
  }

  /**
   * 获取所有事件类型
   * @returns 事件类型数组
   */
  eventNames(): string[] {
    return Object.keys(this.eventType);
  }

  /**
   * 清空所有事件监听器
   */
  clear(): void {
    for (const key in this.eventType) {
      delete this.eventType[key];
    }
  }
}
