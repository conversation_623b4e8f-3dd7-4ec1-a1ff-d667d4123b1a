// 颜色处理工具类

/**
 * RGB颜色值接口
 */
interface RGBColor {
  readonly r: number;
  readonly g: number;
  readonly b: number;
}

/**
 * RGBA颜色值接口
 */
interface RGBAColor extends RGBColor {
  readonly a: number;
}

/**
 * 颜色处理工具类
 * 提供十六进制颜色与RGB/RGBA颜色之间的转换功能
 */
export class ColorHelper {
  private readonly color: string;
  private readonly rgbCache: RGBColor;

  constructor(color: string) {
    if (!this.isValidHexColor(color)) {
      throw new Error(`Invalid hex color: ${color}`);
    }
    this.color = color.toUpperCase();
    this.rgbCache = this.parseHexToRgb(this.color);
  }

  /**
   * 验证十六进制颜色格式
   */
  private isValidHexColor(color: string): boolean {
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexColorRegex.test(color);
  }

  /**
   * 解析十六进制颜色为RGB
   */
  private parseHexToRgb(hex: string): RGBColor {
    const cleanHex = hex.replace('#', '');
    const fullHex = cleanHex.length === 3
      ? cleanHex.split('').map(char => char + char).join('')
      : cleanHex;

    return {
      r: parseInt(fullHex.substring(0, 2), 16),
      g: parseInt(fullHex.substring(2, 4), 16),
      b: parseInt(fullHex.substring(4, 6), 16)
    };
  }

  /**
   * 获取RGB颜色字符串
   * @param alpha 透明度 (0-1)，可选
   * @returns RGB或RGBA颜色字符串
   */
  colorRgb(alpha?: number): string {
    const { r, g, b } = this.rgbCache;

    if (alpha !== undefined) {
      const clampedAlpha = Math.max(0, Math.min(1, alpha));
      return `rgba(${r}, ${g}, ${b}, ${clampedAlpha})`;
    }
    return `rgb(${r}, ${g}, ${b})`;
  }

  /**
   * 获取十六进制颜色字符串
   * @returns 十六进制颜色字符串
   */
  colorHex(): string {
    return this.color;
  }

  /**
   * 获取RGB颜色对象
   */
  getRgb(): RGBColor {
    return { ...this.rgbCache };
  }

  /**
   * 获取RGBA颜色对象
   */
  getRgba(alpha: number = 1): RGBAColor {
    const clampedAlpha = Math.max(0, Math.min(1, alpha));
    return { ...this.rgbCache, a: clampedAlpha };
  }
}

/**
 * 十六进制转RGBA
 * @param hex 十六进制颜色
 * @param alpha 透明度
 * @returns RGBA对象
 */
export function hexToRgba(hex: string, alpha: number = 1): { rgba: string; r: number; g: number; b: number; a: number } {
  const cleanHex = hex.replace('#', '');
  const r = parseInt(cleanHex.substring(0, 2), 16);
  const g = parseInt(cleanHex.substring(2, 4), 16);
  const b = parseInt(cleanHex.substring(4, 6), 16);
  
  return {
    rgba: `rgba(${r}, ${g}, ${b}, ${alpha})`,
    r,
    g,
    b,
    a: alpha
  };
}

/**
 * 创建颜色助手实例
 * @param color 颜色字符串
 * @returns ColorHelper实例
 */
export function createColorHelper(color: string): ColorHelper {
  return new ColorHelper(color);
}
