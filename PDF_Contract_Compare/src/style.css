/* PDF合同对比系统样式 */

:root {
  --primary-color: #4083f7;
  --secondary-color: #fe2027;
  --warning-color: #eca226;
  --success-color: #28a745;
  --background-color: #f8f9fa;
  --text-color: #333;
  --border-color: #dee2e6;
  --shadow: 0 2px 4px rgba(0,0,0,0.1);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
}

/* PDF容器样式 */
.pdfjs {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.viewerContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #f5f5f5;
}

.pdfViewer {
  padding: 10px;
}

.pageContainer {
  position: relative;
  margin: 0 auto 10px;
  border: 1px solid #ccc;
  background: white;
  box-shadow: var(--shadow);
}

/* 加载条样式 */
.loadingBar {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  z-index: 1000;
}

.progress {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.glimmer {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 页码显示 */
.pageNum {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
}

/* 差异高亮样式 */
.diff-highlight-add {
  background-color: rgba(64, 131, 247, 0.3);
  border: 1px solid var(--primary-color);
}

.diff-highlight-delete {
  background-color: rgba(254, 32, 39, 0.3);
  border: 1px solid var(--secondary-color);
}

.diff-highlight-modify {
  background-color: rgba(236, 162, 38, 0.3);
  border: 1px solid var(--warning-color);
}

/* 差异列表样式 */
.differentnr_child {
  padding: 10px;
  margin: 5px 0;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.differentnr_child:hover {
  background-color: #f8f9fa;
  border-color: var(--primary-color);
}

.differentnr_child.on {
  background-color: #e3f2fd;
  border-color: var(--primary-color);
}

.differentnr_child h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
}

.differentnr_child.sc h4 { color: var(--secondary-color); }
.differentnr_child.xg h4 { color: var(--warning-color); }
.differentnr_child.xz h4 { color: var(--primary-color); }

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.btn:hover {
  background: #3066d6;
}

.btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}