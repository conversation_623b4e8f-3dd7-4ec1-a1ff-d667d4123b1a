// PDF合同对比系统主入口文件

import './style.css';
import { PDFCompared } from './classes/PDFCompared.js';
import { configManager } from './utils/configManager.js';
import { createError, isBrowser } from './utils/helpers.js';
import type { CompareItem, PDFObject } from './types/index.js';

// 导出主要类供全局使用
if (isBrowser()) {
  (window as any).PDFCompared = PDFCompared;
  (window as any).PDFCompareConfig = configManager;
}

/**
 * 初始化PDF对比系统
 * @param item 对比数据
 * @param containerId 容器ID，默认为 "main_res"
 * @returns PDFCompared实例
 * @throws {Error} 当参数无效时抛出错误
 */
export function initPDFCompare(item: CompareItem, containerId: string = "main_res"): PDFCompared {
  if (!item) {
    throw createError('CompareItem is required', 'INVALID_PARAMS');
  }

  if (!isBrowser()) {
    throw createError('PDF comparison is only supported in browser environment', 'UNSUPPORTED_ENVIRONMENT');
  }

  const pdfObject: PDFObject = {
    Container: containerId,
    Rev: item
  };

  try {
    return new PDFCompared(pdfObject);
  } catch (error) {
    console.error('Failed to initialize PDF comparison:', error);
    throw error;
  }
}

// 工具函数：修复IE9高度
export function fixIe9Height(): void {
  const windowsHeight = $(window).height()!;
  $('body').css({ height: windowsHeight + 'px' });

  if ($(".embeddedHeader_ofcont").length > 0) {
    $(".embeddedContent").css("height", windowsHeight - 48 + "px");
    $(".svg-container").css("height", windowsHeight - 130 + "px");
    $(".svg-container").css("top", "130px");
  } else {
    $(".embeddedContent").css("height", "100%");
  }
}

// 判断IE版本
export function getIEVersion(): number {
  const userAgent = navigator.userAgent;
  const isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1;
  const isEdge = userAgent.indexOf("Edge") > -1 && !isIE;
  const isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;

  if (isIE) {
    const reIE = new RegExp("MSIE (\\d+\\.\\d+);");
    reIE.test(userAgent);
    const fIEVersion = parseFloat(RegExp["$1"]);

    if (fIEVersion === 7) return 7;
    else if (fIEVersion === 8) return 8;
    else if (fIEVersion === 9) return 9;
    else if (fIEVersion === 10) return 10;
    else return 6; // IE版本<=7
  } else if (isEdge) {
    return 20; // edge
  } else if (isIE11) {
    return 11; // IE11
  } else {
    return 30; // 不是ie浏览器
  }
}

// 全屏切换函数
export function toggleIframeFullscreen(): void {
  const element = document.documentElement;

  if (!document.fullscreenElement) {
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if ((element as any).webkitRequestFullscreen) {
      (element as any).webkitRequestFullscreen();
    } else if ((element as any).msRequestFullscreen) {
      (element as any).msRequestFullscreen();
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }
  }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
  // 设置窗口resize事件
  $(window).resize(() => {
    fixIe9Height();
  });

  // 导出全局函数
  (window as any).fixIe9Height = fixIe9Height;
  (window as any).getIEVersion = getIEVersion;
  (window as any).toggleIframeFullscreen = toggleIframeFullscreen;
  (window as any).initPDFCompare = initPDFCompare;
});

console.log('PDF合同对比系统已加载完成');