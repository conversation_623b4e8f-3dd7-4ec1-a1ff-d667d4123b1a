// PDF合同对比系统类型定义

/**
 * 差异类型枚举
 */
export enum RevisionType {
  ADD = 1,    // 新增
  DELETE = 2, // 删除
  MODIFY = 3  // 修改
}

/**
 * 差异项位置信息
 */
export interface RevisionItem {
  /** 起始位置 */
  readonly startPos: number;
  /** 结束位置 */
  readonly endPos: number;
  /** 文本内容 */
  readonly content: string;
  /** 所在页码 */
  readonly page: number;
}

/**
 * 差异项完整信息
 */
export interface Revision {
  /** 差异类型 */
  readonly type: RevisionType;
  /** 差异项唯一标识 */
  readonly id: number;
  /** 源文件差异信息 */
  readonly srcRev: RevisionItem;
  /** 目标文件差异信息 */
  readonly targetRev: RevisionItem;
}

/**
 * 对比文档信息
 */
export interface CompareItem {
  /** 源文件名称 */
  readonly srcName: string;
  /** 目标文件名称 */
  readonly targetName: string;
  /** 源PDF文件URL */
  readonly srcPdf: string;
  /** 目标PDF文件URL */
  readonly targetPdf: string;
  /** 源文件页数 */
  readonly srcPages: number;
  /** 目标文件页数 */
  readonly targetPages: number;
  /** 对比任务唯一标识 */
  readonly objectId: string;
  /** 差异列表 */
  readonly revisions: readonly Revision[];
}

export interface PDFObject {
  Container: string;
  Rev: CompareItem;
}

/**
 * PDF渲染类型
 */
export type RenderType = 'svg' | 'canvas';

/**
 * 请求类型
 */
export type RequestType = 'fetch' | 'ajax';

/**
 * PDF渲染选项配置
 */
export interface PDFOptions {
  /** PDF文件URL */
  readonly pdfurl?: string;
  /** PDF数据 */
  readonly data?: ArrayBuffer | Uint8Array;
  /** 缩放比例 */
  readonly scale?: number;
  /** 是否启用缩放 */
  readonly zoomEnable?: boolean;
  /** 是否启用滚动 */
  readonly scrollEnable?: boolean;
  /** 是否显示加载条 */
  readonly loadingBar?: boolean;
  /** 是否显示页码 */
  readonly pageNum?: boolean;
  /** 是否显示回到顶部按钮 */
  readonly backTop?: boolean;
  /** 是否启用URI */
  readonly URIenable?: boolean;
  /** 是否支持全屏 */
  readonly fullscreen?: boolean;
  /** 是否懒加载 */
  readonly lazy?: boolean;
  /** 渲染类型 */
  readonly renderType?: RenderType;
  /** 是否支持窗口大小调整 */
  readonly resize?: boolean;
  /** 是否显示文本层 */
  readonly textLayer?: boolean;
  /** 跳转到指定页 */
  readonly goto?: number;
  /** 是否为差异对比模式 */
  readonly isDifference?: boolean;
  /** 差异数组 */
  readonly diffArray?: readonly DiffItem[];
  /** 是否为调试模式 */
  readonly PDF_DEBUG?: boolean;
  /** 页数限制 */
  readonly limit?: number;
  /** 请求类型 */
  readonly type?: RequestType;
  /** 字符映射URL */
  readonly cMapUrl?: string;
  /** HTTP请求头 */
  readonly httpHeaders?: Readonly<Record<string, string>>;
  /** 是否携带凭证 */
  readonly withCredentials?: boolean;
  /** PDF密码 */
  readonly password?: string;
  /** 遇到错误是否停止 */
  readonly stopAtErrors?: boolean;
  /** 是否禁用字体 */
  readonly disableFontFace?: boolean;
  /** 是否禁用范围请求 */
  readonly disableRange?: boolean;
  /** 是否禁用流 */
  readonly disableStream?: boolean;
  /** 是否禁用自动获取 */
  readonly disableAutoFetch?: boolean;
  /** 背景色 */
  readonly background?: string;
  /** 总页数 */
  readonly pagesNum?: number;
}

/**
 * 差异项渲染信息
 */
export interface DiffItem {
  /** 起始位置 */
  readonly startPos: number;
  /** 结束位置 */
  readonly endPos: number;
  /** 文本内容 */
  readonly content: string;
  /** 所在页码 */
  readonly page: number;
  /** 差异项ID */
  readonly id: number;
  /** 差异类型 */
  readonly type: RevisionType;
  /** 填充样式颜色 */
  readonly fillStyle: string;
}

export interface RectOptions {
  id: number;
  fillStyle: ColorHelper;
  lineWidth?: number;
  scale?: number;
  rotation?: number;
  width: number;
  height: number;
  x: number;
  y: number;
  ctx?: CanvasRenderingContext2D;
  page: number;
  str?: string;
}

export interface ColorHelper {
  colorRgb(alpha?: number): string;
  colorHex(): string;
}

export interface FabricRect {
  width: number;
  height: number;
  left: number;
  top: number;
  stroke: ColorHelper;
  fill: string;
  clipName: number;
  page: number;
  strokeWidth: number;
  selectable: boolean;
  id: number;
  hoverCursor: string;
  canvas: any;
  LastPageTop?: number;
  set(props: any): void;
  animate(property: string, value: any, options: any): void;
  on(event: string, handler: (e: any) => void): void;
}

export interface PageCache {
  page: any;
  loaded: boolean;
  container: HTMLElement | null;
  scaledViewport: any;
}

export interface JsonTextData {
  info: {
    page: number;
    scale: number;
  };
  content: TextItem[];
  textIndex: number;
  pileText?: number;
}

export interface TextItem {
  str: string;
  width: number;
  height: number;
  x: number;
  y: number;
  rotation?: number;
  scale?: number;
}

export interface SvgLineOptions {
  btnType: string;
  startLeft: number;
  startTop: number;
  midLeft: number;
  midTop: number;
  endLeft: number;
  endTop: number;
  linkDirection: number;
  linkStatus: number;
}

export interface EventCallback {
  (...args: any[]): void;
}

export interface EventType {
  [key: string]: EventCallback[];
}

// 全局声明
declare global {
  interface Window {
    PDFJS: any;
    fabric: any;
    ActiveXObject?: any;
  }

  var PDFJS: any;
}

// Fabric.js 相关类型
export interface FabricCanvas {
  add(object: any): void;
  renderAll(): void;
  viewportscale: number;
  viewportWidth: number;
  lowerCanvasEl: HTMLCanvasElement;
}

// SVG Line 类接口
export interface ISvgLine {
  initSvgContainer(containerId: string): void;
  drawPolyLine(options: SvgLineOptions): void;
  removeActiveLink(): void;
}
