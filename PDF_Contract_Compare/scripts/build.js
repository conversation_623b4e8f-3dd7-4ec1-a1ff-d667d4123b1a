#!/usr/bin/env node

/**
 * 构建脚本
 * 用于构建PDF合同对比系统的TypeScript版本
 */

import { execSync } from 'child_process';
import { existsSync, rmSync } from 'fs';
import { join } from 'path';

const projectRoot = process.cwd();
const distDir = join(projectRoot, 'dist');

console.log('🚀 开始构建PDF合同对比系统...');

try {
  // 清理旧的构建文件
  if (existsSync(distDir)) {
    console.log('🧹 清理旧的构建文件...');
    rmSync(distDir, { recursive: true, force: true });
  }

  // 类型检查
  console.log('🔍 执行TypeScript类型检查...');
  execSync('npm run type-check', { stdio: 'inherit' });

  // 构建项目
  console.log('📦 构建项目...');
  execSync('npm run build', { stdio: 'inherit' });

  console.log('✅ 构建完成！');
  console.log(`📁 构建文件位于: ${distDir}`);

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
