import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    cors: true
  },

  // 构建配置
  build: {
    // 输出目录
    outDir: 'dist',
    
    // 生成源码映射
    sourcemap: true,
    
    // 构建目标
    target: 'es2022',
    
    // 代码分割
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        example: resolve(__dirname, 'example.html')
      },
      output: {
        // 分块策略
        manualChunks: {
          'pdf-core': ['./src/classes/PDFH5', './src/classes/PDFCompared'],
          'utils': ['./src/utils/colorHelper', './src/utils/eventManager', './src/utils/jerryRect', './src/utils/svgLine']
        }
      }
    },
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },

  // 解析配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@types': resolve(__dirname, 'src/types'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@classes': resolve(__dirname, 'src/classes')
    }
  },

  // 预处理器配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  },

  // 优化配置
  optimizeDeps: {
    include: ['jquery'],
    exclude: ['@types/jquery', '@types/fabric', '@types/pdfjs-dist']
  },

  // 环境变量
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VERSION__: JSON.stringify(process.env.npm_package_version)
  },

  // 插件配置
  plugins: [
    // 可以在这里添加其他Vite插件
  ]
});
