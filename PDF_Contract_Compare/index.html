<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + TS</title>
  </head>
  <body style="height: 784px;">
  <div class="embeddedPages">

      <div class="embeddedHeader_ofcont">

          <div class="embeddedLogo">

              <img src="https://abc.shenht.com/images/logo.png">


          </div>

          <a class="backpre f_right borderRadius" href="https://abc.shenht.com/api/compare/index.htm?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvYmplY3RJZCI6ImY2MDcxYTFkNDAzNTkwYmZmZWI4OTliYWQ5MzQ1MzZlIiwidGltZSI6IjE3NTUxMzQ1MjA2MDgiLCJhcHBJZCI6IjE3MDY2NzA0MzMxOTciLCJtZW1vIjoiQVBJIiwidXNlciI6IntcImlkXCI6XCIxNDQ4Nzg1OTIxMDAwXCIsXCJuYW1lXCI6XCLnrqHnkIblkZhcIixcInJvbGVcIjpcIuezu-e7n-euoeeQhuWRmFwiLFwiYnJhbmNoXCI6XCJzNTIxMVwiLFwib2ZmaWNlQWN0aW9uTmFtZVwiOlwi566h55CG5ZGYXCJ9Iiwic2lnIjoiRURCMEQ2RDI2MjY0QzlCNzU1OTc3REI3MDdBMzczOTMifQ.q_LdcSwnK_4scTAukb17Hhvfn4uXR8xqf2L0G4TUJFY">返回</a>
      </div>

      <div class="embeddedContent comparison_iframeboxs" style="height: 736px;">

          <div class="boxMain mainBox" id="main_res">
              <!--头部	-->
              <div class="boxHeader" style="overflow: inherit;">
                  <div class="operation_items">

                      <a class="downResult" href="https://abc.shenht.com/api/compare/downCompareResultOfExcel.htm?objectId=f6071a1d403590bffeb899bad934536e&amp;token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvYmplY3RJZCI6ImY2MDcxYTFkNDAzNTkwYmZmZWI4OTliYWQ5MzQ1MzZlIiwidGltZSI6IjE3NTUxMzQ1MjA2MDgiLCJhcHBJZCI6IjE3MDY2NzA0MzMxOTciLCJtZW1vIjoiQVBJIiwidXNlciI6IntcImlkXCI6XCIxNDQ4Nzg1OTIxMDAwXCIsXCJuYW1lXCI6XCLnrqHnkIblkZhcIixcInJvbGVcIjpcIuezu-e7n-euoeeQhuWRmFwiLFwiYnJhbmNoXCI6XCJzNTIxMVwiLFwib2ZmaWNlQWN0aW9uTmFtZVwiOlwi566h55CG5ZGYXCJ9Iiwic2lnIjoiRURCMEQ2RDI2MjY0QzlCNzU1OTc3REI3MDdBMzczOTMifQ.q_LdcSwnK_4scTAukb17Hhvfn4uXR8xqf2L0G4TUJFY" target="_blank"><i background="https://abc.shenht.com/images/compare-down.png"></i>下载比对结果</a>
                      <a class="autoScroll"><i></i>同步滚动</a>
                      <a class="pub_screen_2 fullscreenBtn" style="cursor: pointer;" onclick="toggleIframeFullscreen()"><i></i>全屏</a>
                  </div>
                  <div style="clear: both;"></div>
              </div>
              <!--主内容-->
              <div class="pdfBox" id="pdfBox">
                  <table>
                      <tbody>
                      <tr>
                          <td class="df">

                              <!--原pdf		-->
                              <div class="pdfWrapper pdfWrapper-before srcContainer" style="z-index:19;">
                                  <div class="pdfWrapper_title">
                                      原文件：<span id="srcContainerPDFName">技术开发（委托）合同版.docx</span>
                                  </div>
                                  <div class="scrollPages">
                                      <span id="srcContainerpageNumber"></span>
                                      /
                                      <span id="srcContainernumPages"></span>
                                  </div>
                                  <div class="pdfWrapper_box">
                                      <div id="srcContainer" class="pdfWrapper_boxviewer view-box pdfjs">
                                          <div id="srcContainerviewer" class="viewer">

                                          </div>

                                          <!--loading-->
                                          <div id="srcContainerloadingBar" class="loadingBar" style="display: none;">
                                              <div class="progress indeterminate" style="width: 100%;">
                                                  <div class="glimmer"></div>
                                              </div>
                                          </div>
                                          <!--error-->
                                          <div id="srcContainererrorWrapper" class="errorWrapper" hidden="true">
                                              <div class="errorMessageLeft">
                                                  <span id="srcContainererrorMessage" class="errorMessage"></span>
                                                  <button id="srcContainererrorShowMore" class="errorShowMore">
                                                      More Information
                                                  </button>
                                                  <button id="srcContainererrorShowLess" class="errorShowLess">
                                                      Less Information
                                                  </button>
                                              </div>
                                              <div class="errorMessageRight">
                                                  <button id="srcContainererrorClose" class="errorClose">
                                                      Close
                                                  </button>
                                              </div>
                                              <div class="clearBoth"></div>
                                              <textarea id="srcContainererrorMoreInfo" class="errorMoreInfo" hidden="true" readonly="readonly"></textarea>
                                          </div>

                                          <div class="loadingBar" style="display: none;"><div class="progress" style="width: 100%;"> <div class="glimmer"></div> </div></div><div class="pageNum"><div class="pageNum-bg"></div> <div class="pageNum-num"> <span class="pageNow">1</span>/<span class="pageTotal">8</span></div> </div><div class="backTop"></div><div class="loadEffect loading" style="display: none;"></div><div class="viewerContainer"><div class="pdfViewer"><div class="pageContainer pageContainer1" name="page=1" title="Page 1" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg1"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer1" data-page="1" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none; cursor: default;"></canvas></div></div><div class="pageContainer pageContainer2" name="page=2" title="Page 2" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg2"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer2" data-page="2" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer3" name="page=3" title="Page 3" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg3"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer3" data-page="3" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer4" name="page=4" title="Page 4" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg4"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer4" data-page="4" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer5" name="page=5" title="Page 5" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg5"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer5" data-page="5" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer6" name="page=6" title="Page 6" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg6"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer6" data-page="6" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer7" name="page=7" title="Page 7" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg7"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer7" data-page="7" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer8" name="page=8" title="Page 8" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg8"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#srcContainer8" data-page="8" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div></div></div></div>
                                  </div>
                              </div>

                          </td>

                          <td class="simpleWrapper">
                              <div class="simpleWrapper_classic">

                                  <!--窄lists-->
                                  <div class="simple-content">
                                      <div class="sim-h">
                                          <div class="sim-hpage">
                                              <b id="diffNumber">0</b>/<span id="diffTotal">3</span>
                                              <a class="o_zk"></a>
                                          </div>
                                      </div>
                                      <div id="normalDiffList" class="diff-lists"></div>
                                  </div>

                                  <!--宽lists-->
                                  <div class="classicWrapper_showmain">
                                      <div class="differentnums">
                                          <span>共<b id="classicDiffTotal">3</b>处差异</span>
                                          <a class="o_sq"></a>
                                      </div>
                                      <div class="differentnr">
                                          <div id="differentnrList" class="differentnr_position"><div data-id="1" class="differentnr_child sc "><h4>删除</h4><div class="diff_main"><p><label>左：</label><span>整</span></p><p><label>右：</label><span></span></p></div></div><div data-id="2" class="differentnr_child xg "><h4>修改</h4><div class="diff_main"><p><label>左：</label><span>完全支配</span></p><p><label>右：</label><span>30利益提成</span></p></div></div><div data-id="3" class="differentnr_child xg "><h4>修改</h4><div class="diff_main"><p><label>左：</label><span>3</span></p><p><label>右：</label><span>10</span></p></div></div></div>
                                      </div>
                                  </div>

                              </div>
                          </td>

                          <td class="df">

                              <!--目标pdf		-->
                              <div class="pdfWrapper pdfWrapper-after targetContainer" style="z-index:19;">
                                  <div class="pdfWrapper_title">
                                      比对文件：<span id="targetContainerPDFName">技术开发（委托）合同版（条款差异）.docx</span>
                                  </div>
                                  <div class="scrollPages">
                                      <span id="targetContainerpageNumber"></span>
                                      /
                                      <span id="targetContainernumPages"></span>
                                  </div>
                                  <div class="pdfWrapper_box">
                                      <div id="targetContainer" class="pdfWrapper_boxviewer view-box pdfjs">
                                          <div id="targetContainerviewer" class="viewer">

                                          </div>

                                          <!--loading-->
                                          <div id="targetContainerloadingBar" class="loadingBar" style="display: none;">
                                              <div class="progress indeterminate" style="width: 100%;">
                                                  <div class="glimmer"></div>
                                              </div>
                                          </div>
                                          <!--error-->
                                          <div id="targetContainererrorWrapper" class="errorWrapper" hidden="true">
                                              <div class="errorMessageLeft">
                                                  <span id="targetContainererrorMessage" class="errorMessage"></span>
                                                  <button id="targetContainererrorShowMore" class="errorShowMore">
                                                      More Information
                                                  </button>
                                                  <button id="targetContainererrorShowLess" class="errorShowLess">
                                                      Less Information
                                                  </button>
                                              </div>
                                              <div class="errorMessageRight">
                                                  <button id="targetContainererrorClose" class="errorClose">
                                                      Close
                                                  </button>
                                              </div>
                                              <div class="clearBoth"></div>
                                              <textarea id="targetContainererrorMoreInfo" class="errorMoreInfo" hidden="true" readonly="readonly"></textarea>
                                          </div>

                                          <div class="loadingBar" style="display: none;"><div class="progress" style="width: 100%;"> <div class="glimmer"></div> </div></div><div class="pageNum"><div class="pageNum-bg"></div> <div class="pageNum-num"> <span class="pageNow">1</span>/<span class="pageTotal">8</span></div> </div><div class="backTop"></div><div class="loadEffect loading" style="display: none;"></div><div class="viewerContainer"><div class="pdfViewer"><div class="pageContainer pageContainer1" name="page=1" title="Page 1" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg1"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer1" data-page="1" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer2" name="page=2" title="Page 2" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg2"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer2" data-page="2" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer3" name="page=3" title="Page 3" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg3"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer3" data-page="3" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer4" name="page=4" title="Page 4" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg4"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer4" data-page="4" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer5" name="page=5" title="Page 5" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg5"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer5" data-page="5" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer6" name="page=6" title="Page 6" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg6"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer6" data-page="6" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer7" name="page=7" title="Page 7" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg7"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer7" data-page="7" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div><div class="pageContainer pageContainer8" name="page=8" title="Page 8" data-scale="0.7070910684521998" style="max-width: 2381.2px; max-height: 3367.6px;"><div class="loadEffect" style="display: none;"></div><canvas height="1683" width="1190" class="canvasImg8"></canvas><div class="canvas-container" style="width: 100%; height: 100%; position: absolute; user-select: none; top: 0px; left: 0px;"><canvas height="1683" width="1190" id="fabricCanvas#targetContainer8" data-page="8" class="lower-canvas" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas><canvas class="upper-canvas " width="1190" height="1683" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; touch-action: none; user-select: none;"></canvas></div></div></div></div></div>
                                  </div>
                              </div>

                          </td>

                      </tr>
                      </tbody>

                  </table>
              </div>
              <svg disabled="disabled" class="svg-container" id="svg-container" style="height: 654px; top: 130px;"><desc>Created with Snap</desc><defs></defs></svg>

          </div>

          <!--差异内容-->
          <div class="mask-overplay">
              <div class="cdk-box">
                  <div class="box-shadow">
                      <span class="reply_jiao"></span>
                      <h2>新增</h2><!--标题(新增/删除/修改)-->
                      <div class="box-table">
                          <div class="str_left">左：<span>w</span></div>
                          <div class="str_left">右：<span>aaaa</span></div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>

  <script src="https://abc.shenht.com/js/pdfis_es6/jquery3.6.0.min.js"></script>
  <script src="https://abc.shenht.com/js/bootstrap.min.js"></script>
  <script src="https://abc.shenht.com/js/nanoscroller.js"></script>
  <script src="https://abc.shenht.com/js/public.js?v=1755067817890"></script>

  <script src="https://abc.shenht.com/js/pdfis_es6/Snap.svg.js"></script>
  <script src="https://abc.shenht.com/js/pdfis_es6/fabric.js"></script>
  <script src="https://abc.shenht.com/js/pdfis_es6/pdf.combined.js"></script>
  <script src="https://abc.shenht.com/js/pdfis_es6/pdfH5.js?v=1755067817890"></script>
  <script src="https://abc.shenht.com/js/pdfis_es6/pdfCompared2.js?v=1755067817890"></script>
  <script src="https://abc.shenht.com/js/iframeAction.js?v=1755067817890"></script>
  <script>

      var host="https:\/\/abc.shenht.com";
      var fIEVersion=IEVersion();
      if(fIEVersion <=11){
          document.write("<script src='"+host+"/js/pdfis_es6/symbol-es6.min.js'><\/script>");
          document.write("<script src='"+host+"/js/pdfis_es6/tool.js'><\/script>");
          document.write("<script src='"+host+"/js/pdfis_es6/compatibility.js'><\/script>");
      }

      function fixIe9Heigt(){
          //修补ie9
          //var pdfH=$("#pdfBox").height();
          var windowsHeight=$(window).height();
          //$("#pdfBox").css("height",windowsHeight-48+"px");
          $('body').css({height: windowsHeight+'px'});
          if($(".embeddedHeader_ofcont").length > 0){
              $(".embeddedContent").css("height",windowsHeight-48+"px");
              $(".svg-container").css("height",windowsHeight-130+"px");
              $(".svg-container").css("top","130px");
          }else{
              $(".embeddedContent").css("height","100%");

          }
      }
      $(window).resize(function() {
          fixIe9Heigt();
      });
      $(function(){
          /*<![CDATA[*/
          var item = {"srcName":"\u6280\u672F\u5F00\u53D1\uFF08\u59D4\u6258\uFF09\u5408\u540C\u7248.docx","targetName":"\u6280\u672F\u5F00\u53D1\uFF08\u59D4\u6258\uFF09\u5408\u540C\u7248\uFF08\u6761\u6B3E\u5DEE\u5F02\uFF09.docx","srcPdf":"https:\/\/abc.shenht.com\/loadDocument.docx?file=%2Fdata%2Fwebapp%2Fwriteable%2Fabc%2Ffiles%2F%2F20250717%2F48f4d45ee706570de70b4e2076eb989c.pdf\u0026name=%E6%8A%80%E6%9C%AF%E5%BC%80%E5%8F%91%EF%BC%88%E5%A7%94%E6%89%98%EF%BC%89%E5%90%88%E5%90%8C%E7%89%88\u0026ts=1755135279058\u0026dec=0\u0026appId=1706670433197\u0026sig=079e01846b57dcc64b0f64a0211b3a5c\u0026ignoreHeaderAndFooter=true","targetPdf":"https:\/\/abc.shenht.com\/loadDocument.docx?file=%2Fdata%2Fwebapp%2Fwriteable%2Fabc%2Ffiles%2F%2F20250717%2Feb6d608d46401f4123dde73bbab1bb8a.pdf\u0026name=%E6%8A%80%E6%9C%AF%E5%BC%80%E5%8F%91%EF%BC%88%E5%A7%94%E6%89%98%EF%BC%89%E5%90%88%E5%90%8C%E7%89%88\u0026ts=1755135279058\u0026dec=0\u0026appId=1706670433197\u0026sig=2a04c764fdfc01b634c589812dd4829d\u0026ignoreHeaderAndFooter=true","srcPages":8,"targetPages":8,"objectId":"f6071a1d403590bffeb899bad934536e","revisions":[{"type":2,"id":1,"srcRev":{"startPos":2347,"endPos":2348,"content":"\u6574","page":4},"targetRev":{"startPos":2347,"endPos":2347,"content":"","page":4}},{"type":3,"id":2,"srcRev":{"startPos":2956,"endPos":2960,"content":"\u5B8C\u5168\u652F\u914D","page":5},"targetRev":{"startPos":2954,"endPos":2960,"content":"30\u5229\u76CA\u63D0\u6210","page":5}},{"type":3,"id":3,"srcRev":{"startPos":3033,"endPos":3034,"content":"3","page":5},"targetRev":{"startPos":3035,"endPos":3037,"content":"10","page":5}}]};
          /*]]>*/
          setTimeout(function(){
              fixIe9Heigt();
          },10);
          var pdfObject={
              Container:"main_res",
              Rev:item,
              Container:"pdfBox",
          }
          new PDFCompared(pdfObject);

          iframeload();
          //获取二维码
          /*$("#pageCodeUrl").attr('src',"/api/common/loadAuthQR.htm?objectId=${objectId}&token=${token}&type=2");*/



          //二维码
          $(".phoneewm").hover(function() {
              $(".ewmscan").removeClass("hide");
          }, function() {
              $(".ewmscan").addClass("hide");
          });

      });

      /**
       * 判断ie版本
       * @returns {number}
       * @constructor
       */
      function IEVersion() {
          var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
          var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
          var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
          var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
          if (isIE) {
              var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
              reIE.test(userAgent);
              var fIEVersion = parseFloat(RegExp["$1"]);
              if (fIEVersion == 7) {
                  return 7;
              } else if (fIEVersion == 8) {
                  return 8;
              } else if (fIEVersion == 9) {
                  return 9;
              } else if (fIEVersion == 10) {
                  return 10;
              } else {
                  return 6; //IE版本<=7
              }
          } else if (isEdge) {
              return 20; //edge
          } else if (isIE11) {
              return 11; //IE11
          } else {
              return 30; //不是ie浏览器
          }
      }

      /*function downResultOfExcel(){
      window.open('/api/compare/downCompareResultOfExcel.htm?objectId=${objectId}]]&token=${token}');
    }*/


  </script>


  </body>
</html>
